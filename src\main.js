import './assets/main.css'
import 'animate.css';
import WOW from 'wow.js';
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import VueLazyload from 'vue3-lazy';
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.use(VueLazyload, {
    loading: '',
    error: '',
    lifecycle: {
        loading: (el) => {
            console.log('image loading', el);
        },
        error: (el) => {
            console.log('image error', el);
        },
        loaded: (el) => {
            console.log('image loaded', el);
        },
    }
})
app.mount('#app')
// 初始化 WOW.js
const wow = new WOW();
wow.init();
