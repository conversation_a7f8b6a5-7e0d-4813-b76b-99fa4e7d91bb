

<template>
  <div class="student-info-wrapper">
    <div class="student-header">
      <div class="student-avatar">
        <img :src="userInfo.avatar || defualtIcon" alt="student-avatar" class="student-avatar-img" />
      </div>
      <div class="student-name">{{userInfo.userName || '游客'}}</div>
    </div>
    <div class="student-class">
      <div class="student-class-item" v-for="(item, index) in classList" :key="index">
        <div class="class-img">
          <img :src="item.img" alt="class-img" class="img" />
        </div>
        <div class="class-name">{{item.name}}</div>
      </div>
    </div>
    <div class="student-footer">
      <div class="student-footer-item">
        <div class="footer-date">35</div>
        <div class="footer-text">学习(h)</div>
      </div>
      <div class="student-footer-item">
        <div class="footer-date">168</div>
        <div class="footer-text">刷题</div>
      </div>
    </div>
    <div v-if="userInfo.userName" class="logout" @click="logout">退出登录</div>
  </div>
</template>

<script setup>
  import { defineProps, onMounted, ref, watch } from 'vue'
  import kecheng from '@/assets/image/kecheng.png'
  import jiangyi from '@/assets/image/jiangyi.png'
  import tiku from '@/assets/image/tiku.png'
  import defualtIcon from '@/assets/image/defualt.png'
  const classList = [
    {
      name: '课程',
      img: kecheng
    },
    {
      name: '题库',
      img: tiku
    },
    {
      name: '讲义',
      img: jiangyi
    },
  ]

  let props = defineProps({
    studentInfo: {
      type: Object,
      default: () => ({})
    }
  })

  const logout = () => {
    localStorage.removeItem('userInfo')
    localStorage.removeItem('token')
    localStorage.removeItem('studentInfo')
    window.location.href = '/teachingExam'
    // window.location.reload()
  }

  const userInfo = ref({})

  onMounted(() => {
    if (localStorage.getItem('userInfo')) {
      userInfo.value = JSON.parse(localStorage.getItem('userInfo'))
    }
  })

  watch(() => props.studentInfo, (newVal) => {
    userInfo.value = newVal
  })


</script>

<style scoped lang="less">
  .student-info-wrapper {
    width: 26%;
    height: 5.2rem;
    background: rgba(17,17,17,0.5);
    border-radius: 0.12rem;
    border: 1px solid rgba(45, 45, 45, 1);
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0.26rem;
    .student-header {
      display: flex;
      flex-direction: column;
      .student-avatar {
        width: 0.8rem;
        height: 0.8rem;
        border-radius: 50%;
        border: 2px solid transparent;
        background-image: linear-gradient(to right, rgba(202, 123, 255, 1), rgba(128, 251, 255, 1));
        margin-bottom: 0.14rem;
        .student-avatar-img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .student-name {
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.18rem;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
    .student-class {
      width: 100%;
      margin-top: 0.4rem;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .student-class-item {
        display: flex;
        align-items: center;
        flex-direction: column;
        cursor: pointer;
        .class-img {
          .img {

          }
        }
        .class-name {
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 0.18rem;
          color: #FFFFFF;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .student-footer {
      width: 100%;
      margin-top: 0.4rem;
      display: flex;
      justify-content: space-around;
      .student-footer-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .footer-date {
          font-family: Noto Sans SC;
          font-weight: bold;
          font-size: 0.28rem;
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-bottom: 0.05rem;
        }
        .footer-text {
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 0.16rem;
          color: #FFFFFF;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .logout {
      // padding: 0.1rem 0.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 0.5rem;
      padding: 0 20px;
      border-radius: 0.08rem;
      font-family: Noto Sans SC;
      font-size: 0.18rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      margin-top: 0.35rem;
      cursor: pointer;
      border: 2px solid #333;
      cursor: pointer;
      position: relative;
      background-color: transparent;
      overflow: hidden;
      z-index: 1;
      &:hover {
        border: 2px solid rgb(252, 70, 100);
      }
    }
    .logout::before {
      content: "";
      position: absolute;
      left: 0px;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgb(252, 70, 100);
      transform: translateX(-101%);
      transition: all .3s;
      z-index: -1;
    }
    .logout:hover::before {
      transform: translateX(0);
    }
  }
</style>