import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/teachingExam',
      name: 'teachingExam',
      children: [
        {
          path: '',
          name: '/',
          component: () => import('../views/TeachingExam/TeachingExam.vue')
        },
        {
          path: 'echarts',
          name: 'echarts',
          component: () => import('../views/TeachingExam/TeachingExamEcharts.vue')
        },
        {
          path: 'detail',
          name: 'detail',
          component: () => import('../views/TeachingExam/TeachingExamDetail.vue')
        }
      ]
    },
    {
      path: '/artCommunity',
      name: 'artCommunity',
      component: () => import('../views/ArtCommunity/index.vue')
    },
    {
      path: '/collaborationConsultation',
      name: 'collaborationConsultation',
      component: () => import('../views/CollaborationConsultation.vue')
    },
    {
      path: '/joinUs',
      name: 'joinUs',
      component: () => import('@/views/JoinUs.vue')
    },
    {
      path: '/companyProfile',
      name: 'companyProfile',
      component: () => import('../views/CompanyProfile.vue')
    },
    {
      path: '/digitalIndustry',
      name: 'digitalIndustry',
      component: () => import('../views/Cases.vue')
    },
    {
      path: '/digitalArt',
      name: 'digitalArt',
      component: () => import('../views/CasesYS.vue')
    },
    {
      path: '/digitalEducation',
      name: 'digitalEducation',
      component: () => import('../views/CasesJY.vue')
    }/* ,
    {
      path: '/demo',
      name: 'demo',
      component: () => import('../views/Demo.vue')
    } */
  ]
})

router.beforeEach((to, from, next) => {
  if (to.fullPath.includes('code')) {
    localStorage.setItem('code', to.query.code)
    next("/teachingExam")
  }
  setTimeout(() => {
    window.scrollTo(0, 0); // 将滚动位置设置为顶部
  },100)
  next();
});

export default router
