

<template>
  <div class="teaching-exam-warpper">
    <div class="teaching-exam-btns">
      <!-- <div :class="tabIndex == '1' ? 'teaching-exam-btn-item active' : 'teaching-exam-btn-item'" @click="changeTab('1')">课程</div> -->
      <!-- <div :class="tabIndex == '2' ? 'teaching-exam-btn-item active marginLeft24' : 'teaching-exam-btn-item marginLeft24'" @click="changeTab('2')">讲义</div> -->
    </div>
    <div class="teaching-exam-content">
      <div class="teaching-exam-content-left">
        <div v-if="tabIndex === '1'" class="teaching-exam-content-item" v-for="(item, index) in classList" :key="index" @click="handleClick(item)">
          <div class="item-left">
            <img :src="item.thumb" alt="" class="left-img" />
          </div>
          <div class="item-right">
            <div class="item-titles">
              <div class="item-title-b">{{item.name}}</div>
            </div>
            <div class="item-content">
              <div class="text">学习次数 {{item.count}}</div>
              <div class="silder">
                <div class="silder-bar" :style="{'--progress-value': item.progress + '%'}"></div>
                <div class="silder-text">{{item.progress}}%</div>
              </div>
            </div>
            <div class="item-date">{{item.createTime}}</div>
          </div>
        </div>
        <div v-if="tabIndex === '2'">
          <Handout />
        </div>
      </div>
      <StudentInfo :studentInfo="studentInfo" />
    </div>

    <LoginDialog :isShow="isShowLogin" @closeDialog="closeDialog" />
    
  </div>
</template>

<script setup>
  import { ref, onMounted} from 'vue';
  import axios from 'axios';
  import StudentInfo from '@/components/StudentInfo.vue';
  import LoginDialog from '@/components/LoginDialog.vue';
  import Handout from '@/components/Handout.vue';
  import { useRoute, useRouter } from 'vue-router'
  const router = useRouter()
  const route = useRoute()

  const studentInfo = ref({})
  const classList = ref([])

  let tabIndex = ref('1');
  let isShowLogin = ref(false);

  const changeTab = (index) => {
    tabIndex.value = index;
  }
  const closeDialog = (val) => {
    isShowLogin.value = false;
    if (val) {
      getInfo()
    }
  }

  const getInfo = () => {
    // https://easyart.cc/prod-api/getInfo
    // http://************:8180
    axios.get(`https://easyart.cc/prod-api/getInfo`, {
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      }
    }).then(({data}) => {
      if(data.code === 200) {
        if (data.course.length > 0) {
          classList.value = data.course
          localStorage.setItem('studentInfo', JSON.stringify(data.course))
        }
        studentInfo.value = data.user
        localStorage.setItem('userInfo', JSON.stringify(data.user))
      }
      if(data.code === 401) {
        localStorage.removeItem('token')
        isShowLogin.value = true;
        ElMessage.warning('请先登录！')
      }
    }) 
  }
  
  const handleClick = (item) => {
    let token = localStorage.getItem('token')
    if (!token) {
      isShowLogin.value = true;
      return
    }
    router.push({
      path: '/teachingExam/detail',
      query: {
        index: item.id
      }
    })
  }

  // let studyDate = ref('2024年7月17日')

  onMounted(() => {
    /* let code = localStorage.getItem('code')
    let token = localStorage.getItem('token')
    if(code !== null && (token === null || undefined)) {
      axios.post(`https://www.easyart.cc/prod-api/wx/wxLogin?code=${code}`).then(({data}) => {
        ElMessage.success(data.msg)
        studentInfo.value = data?.data?.user
        localStorage.setItem('token', data.data.token)
        localStorage.setItem('studentInfo', JSON.stringify(data.data.user))
        localStorage.removeItem('code')
      }).catch((err) => {
        console.log(err)
        localStorage.removeItem('code')
      })
    } */
    getInfo()
    /* let date = new Date()
    studyDate.value = date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + (date.getDate() - 1) + '日' */

  })

</script>

<style lang="less" scoped>
  .teaching-exam-warpper {
    width: 60%;
    margin: 0 auto;
    padding: 1.2rem 0 1rem 0;
    .teaching-exam-btns {
      display: flex;
      align-items: center;
      margin-bottom: 0.24rem;
      .teaching-exam-btn-item {
        width: 1.5rem;
        height: 0.5rem;
        display: flex;
        font-size: 0.2rem;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
        border-radius: 26px;
        border: 1px solid rgba(255,255,255,0.48);
      }
      .active {
        background-image: linear-gradient(to right, #8e9eab 0%, #eef2f3  51%, #8e9eab  100%);
        transition: 0.5s;
        background-size: 200% auto;
        color: #000;
        &:hover {
          background-position: right center;
          color: #000;
          text-decoration: none;
        }
      }
    }
    .teaching-exam-content {
      display: flex;
      .teaching-exam-content-left {
        flex: 1;
        margin-right: 0.38rem;
        .teaching-exam-content-item {
          width: 100%;
          height: 2rem;
          padding: 0.24rem;
          display: flex;
          align-items: center;
          margin-bottom: 0.2rem;
          background: rgba(17,17,17,0.5);
          border-radius: 0.12rem;
          border: 1px solid #2d2d2d;
          cursor: pointer;
          .item-left {
            width: 1.7rem;
            height: 1.7rem;
            margin-right: 0.2rem;
            border-radius: 0.08rem;
            border: 1px solid #2d2d2d;
            .left-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 0.08rem;
              filter: drop-shadow(2px 2px 3px #B6D7FF);
            }
          }
          .item-right {
            display: flex;
            flex-direction: column;
            .item-titles {
              display: flex;
              align-items: center;
              margin-bottom: 0.24rem;
              cursor: pointer;
              .item-title-s {
                width: 0.7rem;
                height: 0.28rem;
                background: #FFFFFF;
                font-family: Noto Sans SC;
                font-weight: 500;
                font-size: 0.14rem;
                color: #030303;
                line-height: 0.28rem;
                text-align: center;
                font-style: normal;
                text-transform: none;
                border-radius: 0.04rem;
              }
              .item-title-b {
                font-family: Noto Sans SC;
                font-weight: bold;
                font-size: 0.26rem;
                color: #FFFFFF;
                font-style: normal;
                text-transform: none;
                letter-spacing: 0.02rem;
              }
            }
            .item-content {
              margin-bottom: 0.24rem;
              display: flex;
              align-items: center;
              .text {
                font-family: Noto Sans SC;
                font-weight: bold;
                font-size: 0.18rem;
                color: #FFFFFF;
                font-style: normal;
                text-transform: none;
                margin-right: 0.18rem;
              }
              .silder {
                width: 4.5rem;
                height: 0.08rem;
                background: #FFFFFF;
                border-radius: 0.06rem;
                position: relative;
                display: flex;
                align-items: center;
                .silder-bar {
                  // width: 0%;
                  --progress-value: 0;
                  height: 0.08rem;
                  background: linear-gradient( 270deg, #D579FF 0%, #B6D7FF 46%, #8DFFE8 100%), #F3F2FF;
                  box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
                  border-radius: 0.26rem;
                  z-index: 8;
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: var(--progress-value); /* 宽度使用CSS变量 */
                  transition: width 2s ease-out; /* 动画过渡效果 */
                }
                .silder-text {
                  position: absolute;
                  top: -0.08rem;
                  right: -0.44rem;
                }
              }
            }
            .item-date {
              font-family: Noto Sans SC;
              font-weight: 400;
              font-size: 0.16rem;
              color: #999999;
              line-height: 0.16rem;
              font-style: normal;
              text-transform: none;
            }
          }
          &:hover {
            filter: drop-shadow(2px 3px 3px #3a3939);
            .item-right {
              .item-titles {
                .item-title-b {
                  color: #8dffe8;
                }
              }
            }
          }
        }
      }
    }
  }
</style>