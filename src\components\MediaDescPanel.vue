<template>
  <div class="media-desc-panel">
    <div
      class="media-block"
      v-for="(item, index) in items"
      :key="index"
      :class="item.size"
    >
      <img v-if="item.type === 'img'" :src="item.src" class="media-img" />
      <video
        v-else-if="item.type === 'video'"
        :src="item.src"
        controls
        class="media-video"
      ></video>
      <div class="media-text" v-if="item.desc">{{ item.desc }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MediaDescPanel",
  props: {
    items: {
      type: Array,
      required: true
    }
  }
};
</script>

<style scoped >
.media-desc-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
}

.media-block {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

/* 尺寸样式 */
.media-size-full {
  width: 100%;
}

.media-size-half {
  width: calc(50% - 0.1rem);
}

.media-size-grid {
  width: calc(33.33% - 0.2rem);
}

/* 媒体元素样式 */
.media-img,
.media-video {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  border-radius: 0.1rem;
  transition: all 0.3s ease;
}

.media-img:hover,
.media-video:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 描述文本样式 */
.media-text {
  margin-top: 0.15rem;
  font-size: 0.22rem;
  color: #444;
  text-align: center;
  line-height: 1.4;
  padding: 0 0.1rem;
}

/* 错误提示样式 */
.media-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0.1rem 0.2rem;
  border-radius: 4px;
  font-size: 0.18rem;
  display: flex;
  align-items: center;
  gap: 0.05rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .media-size-half,
  .media-size-grid {
    width: 100%;
  }
  
  .media-text {
    font-size: 0.18rem;
  }
}
</style>
<!-- 调用方法<MediaDescPanel :items="mediaList" /> -->
 
 <!-- data() {
  return {
    mediaList: [
      { type: "img", src: "1.jpg", desc: "图1说明", size: "full" },
      { type: "img", src: "2.jpg", desc: "图2说明", size: "half" },
      { type: "video", src: "demo.mp4", desc: "视频说明", size: "half" },
      { type: "img", src: "3.jpg", desc: "图3", size: "grid" },
      { type: "img", src: "4.jpg", size: "grid" }
    ]
  };
} -->
