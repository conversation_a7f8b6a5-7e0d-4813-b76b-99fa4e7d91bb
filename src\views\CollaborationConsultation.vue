<template>
  <div class="collaboration-consultation" v-if="!isSuccess">
    <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="2s">合作咨询</div>
    <div class="sub-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">感谢您对我们的关注，我们非常欢迎您咨询我们的业务。我们将以卓越的服务和高质量的产品来满足您的需求。请随时联系我们，让我们开始合作吧！</div>
    <div class="consultation-content">
      <div class="consultation-warp">
        <div class="consultation-title">预约咨询</div>
        <div class="consultation-form">
          <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="auto" label-position="top" size="large">
            <el-row :gutter="24">
              <el-col :span="12" class="wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入您的姓名" maxlength="15"/>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">
                <el-form-item label="职务">
                  <el-input v-model="form.job" placeholder="请输入您的职务"  maxlength="30"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12" class="wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
                <el-form-item label="电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入您的电话" maxlength="11" />
                </el-form-item>
              </el-col>
              <el-col :span="12" class="wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">
                <el-form-item label="所在行业">
                  <el-input v-model="form.industry" placeholder="请输入您的所在行业" maxlength="30"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12" class="wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="form.email" placeholder="请输入您的邮箱" maxlength="50"/>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">
                <el-form-item label="业务类型">
                  <el-select v-model="form.type" placeholder="请选择业务类型">
                    <el-option label="校企合作" value="校企合作" />
                    <el-option label="数字艺术" value="数字艺术" />
                    <el-option label="数字媒体" value="数字媒体" />
                    <el-option label="私有化定制" value="私有化定制" />
                    <el-option label="数字人" value="数字人" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12" class="wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
                <el-form-item label="公司名称">
                  <el-input v-model="form.companyName" placeholder="请输入您的公司名称" maxlength="50"/>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">
                <el-form-item label="公司规模">
                  <el-select v-model="form.companyPeople " placeholder="请选择公司规模" class="xxxxxxxxx">
                    <el-option label="1-20" value="1-20" />
                    <el-option label="20-300" value="20-300" />
                    <el-option label="300-3000" value="300-3000" />
                    <el-option label="1000-5000" value="1000-5000" />
                    <el-option label="5000以上" value="5000以上" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="consultation-btn wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
          <div class="book-now" @click="onSubmit(ruleFormRef)">{{isLoading ? '预约中...' : '立即预约'}}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="consultation-success-warpper" v-else>
    <div class="successIcon">
      <img :src="successIcon" alt="success" class="img" />
    </div>
    <div class="consultation-success">预约成功</div>
    <div class="consultation-success-sub">您已提交，我们会尽快与您联系，邀请您添加爱简艺企微客服，</div>
    <div class="consultation-success-sub">以便向您同步最新进展</div>
    <div class="erweima">
      <img :src="wxIcon" alt="wx" class="img" />
    </div>
    <div class="text">扫一扫添加爱简艺企微客服</div>
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import axios from 'axios';
  import successIcon from '@/assets/image/success.png'
  import wxIcon from '@/assets/image/wx.png'
  const isLoading = ref(false);
  const isSuccess = ref(false);
  const ruleFormRef = ref();
  const form = reactive({
    name: '',
    job: '',
    phone: '',
    industry: '',
    email: '',
    type: '校企合作',
    companyName: '',
    companyPeople: '1-20'
  })

  const rules = reactive({
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change'],
      }
    ],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      {
        pattern: /^1[3456789]\d{9}$/,
        message: '请输入正确的手机号',
        trigger: ['blur', 'change'],
      }
    ],
  })

  const onSubmit = async (ruleFormRef) => {
    if (!ruleFormRef) return
    await ruleFormRef.validate((valid, fields) => {
      if (valid) {
        isLoading.value = true
        axios.post('https://www.easyart.cc/prod-api/sys-cooperation/addCooperation', form).then(res => {
          setTimeout(() => {
            isLoading.value = false
            /// ElMessage.success('预约成功！我们会尽快与您联系。')
            isSuccess.value = true
          }, 2000)
        })
      } else {
        isSuccess.value = false
        isLoading.value = false
        console.log('error submit!', fields)
      }
    })
  }
  
</script>

<style lang="less" scoped>
  .collaboration-consultation {
    width: var(--width-value-warp);
    margin: 0 auto;
    padding: 1rem 0 1rem 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    .big-title {
      margin-top: 0.6rem;
      margin-bottom: 0.3rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.44rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .sub-title {
      max-width: 760px;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.22rem;
      color: rgba(255,255,255,1);
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-bottom: 0.5rem;
    }
    .consultation-content {
      width: 100%;
      padding-bottom: 0.5rem;
      background: rgba(17,17,17,0.5);
      box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.3);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-image: linear-gradient(180deg, rgba(110, 110, 110, 0.3), rgba(45, 45, 45, 0.1)) 1 1;
      .consultation-warp {
        max-width: 760px;
        margin: 0 auto;
        .consultation-title {
          margin-top: 38px;
          margin-bottom: 20px;
          font-family: Noto Sans SC;
          font-weight: 500;
          font-size: 20px;
          color: #FFFFFF;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .consultation-form {
          /deep/ .el-form {
            .el-form-item {
              margin-bottom: 0.24rem;
              .el-form-item__label {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.2rem;
                color: #FFFFFF;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-bottom: 0.16rem;
              }
              .el-form-item__content {
                .el-input {
                  --el-input-bg-color: #121212;
                  --el-input-border-color: #666;
                  --el-input-hover-border-color:#c0c4cc;
                  --el-input-focus-border:#c0c4cc;
                  --el-input-focus-border-color: #c0c4cc;
                  --el-input-text-color: #fff;
                  --el-input-placeholder-color: #666;
                }
                .el-input--large {
                  --el-input-height: 0.6rem;
                  font-size: 0.2rem;
                  font-family: Noto Sans SC;
                }
                .el-select {
                  --el-select-font-size: 0.2rem;
                  --el-select-input-font-size: 0.2rem;
                  --el-select-input-height: 0.6rem;
                  --el-select-input-border-color: #666;
                  --el-select-input-hover-border-color: #c0c4cc;
                  --el-select-input-focus-border-color: #c0c4cc;
                  --el-select-input-text-color: #fff;
                  --el-select-dropdown-border-color: #666;
                }
                .el-select--large {
                  height: 0.56rem;
                  .el-select__wrapper {
                    height: 100%;
                    background-color: #121212;
                    box-shadow: 0px 0px 0px 1px #666;
                    font-size: 0.2rem;
                    .el-select__input {
                      color: #fff;
                    }
                    .el-select__placeholder {
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
        }
        .consultation-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 0.4rem;
          .book-now {
            color: rgb(255, 255, 255);
            height: 0.58rem;
            width: 2.5rem;
            line-height: 0.58rem;
            border: 1px solid #fff;
            border-radius: 0.06rem;
            font-size: 0.2rem;
            cursor: pointer;
            text-align: center;
            letter-spacing: 0.02rem;
            &:hover {
              transform: scale(1.02);
            }
          }
        }
      }
    }
  }
  .consultation-success-warpper {
    width: var(--width-value-warp);
    margin: 1.8rem auto 0 auto;
    padding-top: 0.6rem;
    display: flex;
    align-items: center;
    flex-direction: column;
    background: rgba(17,17,17,0.5);
    box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-image: linear-gradient(180deg, rgba(110, 110, 110, 0.3), rgba(45, 45, 45, 0.1)) 1 1;
    .successIcon {
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      .img {
        width: 1.2rem;
        height: 1.2rem;
      }
    }
    .consultation-success {
      margin-top: 0.4rem;
      font-size: 0.3rem;
      margin-bottom: 0.2rem;
    }
    .consultation-success-sub {
      color: #999;
      font-size: 0.22rem;
    }
    .erweima {
      margin-top: 0.4rem;
      margin-bottom: 0.4rem;
      width: 2.5rem;
      height: 2.5rem;
      padding: 0.12rem;
      border: solid 1px #ccc;
      .img {
        width: 100%;
        height: 100%;
      }
    }
    .text {
      color: #999;
      font-size: 0.22rem;
    }
  }
</style>