/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;
  --width-value: 12rem;
  --width-value-div: 70%;
  --width-value-warp: 1200px;
  --width-header-value: 65%;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

@font-face {
  font-family: 'Noto Sans SC';
  src: url('@/assets/fonts/NotoSansSC-6.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  font-weight: normal;
  box-sizing: border-box;
}
html{
  font-size: 100px;
  background: #000000;
}
body {
  overflow-y: auto;
  min-height: 100vh;
  color: #fff;
  transition:color 0.5s, background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    'Noto Sans SC',
    sans-serif;
  font-size: 14px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@media screen and (min-width: 1920px) {
  html {
    font-size: 75px;
  }
}
@media screen and (min-width: 2500px) {
  html {
    font-size: 100px;
  }
  
}
@media screen and (min-width: 1366px) and (max-width: 1919px) {
  html {
    font-size: 65px;
  }
  :root {
    --width-header-value: 70%;
  }
}

@media screen and (max-width: 1366px) {
  html {
    font-size: 60px;
    min-width: 1300px;
    overflow-y: scroll;
  }
  body {
    overflow-y: scroll;
  }
  :root {
    --width-header-value: 70%;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background-color: #0000000d;
}
::-webkit-scrollbar-thumb {
  background-color: #9093994d;
  border-radius: 2px;
  box-shadow: inset 0 0 6px #0003;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #b6b7b9;
}

#app {
  height: 100%;
  font-weight: normal;
}

.bgColor {
  background: url('/src/assets/image/btnBg.png') no-repeat !important;
  background-size: 100% 100% !important;
  border: solid 2px #fff !important;
}
.btnBg2 {
  background: url('/src/assets/image/btnBg2.png') no-repeat !important;
  background-size: 100% 100% !important;
  border: solid 1px #fff !important;
  /* background: #7ccfc3 !important;
  color: #030303 !important; */
}

.marginLeft24 {
  margin-left: 24px;
}
.marginBottom20 {
  margin-bottom: 20px;
}

.el-dialog {
  background: rgba(34,34,34,0.9) !important;
  box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.3) !important;
  border-radius: 12px 12px 12px 12px !important;
}