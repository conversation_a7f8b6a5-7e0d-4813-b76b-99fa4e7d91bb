<script setup>

  import { ref } from 'vue'

  const activeNames = ref([1])
  const handleChange = (val) => {
    console.log(val)
  }

  const joinUsList = [
    {
      id: 1,
      title: '前端开发工程师',
      content:[
        {
          name: '工作职责',
          value: [
            "1，领导前端项目，从概念设计到产品发布的全周期开发。",
            "2，设计和实现高性能、高可用性的前端应用。",
            "3，与设计师、后端工程师和产品经理紧密合作，确保技术实现符合产品愿景。",
            "4，指导和培养初级开发人员，提升团队整体技术水平。",
            "5，跟踪和评估前端技术发展趋势，引入创新解决方案。",
          ]
        },
        {
          name: '职位要求',
          value: [
            "1，拥有计算机科学或相关领域的本科学士学位。",
            "2，至少5年以上的前端开发经验。",
            "3，精通现代JavaScript、HTML5和CSS3。",
            "4，熟练掌握至少一个主流前端框架，如React.js、Vue.js或Angular。",
            "5，熟悉前端工程化工具，如Webpack、Babel、PostCSS等。",
            "6，对前端性能优化有深入理解，包括代码分割、懒加载、服务端渲染等技术。",
            "7，熟悉响应式设计和跨浏览器兼容性问题。",
            "8，熟悉版本控制系统，特别是Git。",
            "9，有使用前端测试框架和工具的经验，如Jest、Mocha、Cypress等。",
            "10，熟悉前端安全最佳实践，包括XSS和CSRF防护。",
            "11，具备良好的团队合作精神和领导能力。",
            "12，优秀的英语书面和口头沟通能力。",
          ]
        },
        {
          name: '加分项',
          value: [
            "1，具有Node.js和Express.js开发经验。",
            "2，熟悉Docker、Kubernetes等容器化技术。",
            "3，有领导大型前端项目或团队的经验。",
            "4，对前端新技术有持续的研究和探索。",
            "5，了解AI技术，如GPT和Stable Diffusion，并有相关项目经验。",
          ]
        }

      ]
    },
    {
      id: 2,
      title: '后端开发工程师',
      content: [
        {
          name: '工作职责',
          value : [
            "1，使用Java语言设计和实现可扩展的后端服务。",
            "2，优化系统性能，确保数据的一致性和系统的高可用性。",
            "3，使用现代技术栈解决复杂的技术问题。",
          ]
        },
        {
          name: '基本要求',
          value: [
            "1，熟悉掌握Java编程语言和面向对象设计原则。",
            "2，熟悉Kafka、ZooKeeper、Nginx、Spring等后端技术。",
            "3，理解Maven项目构建和依赖管理。",
            "4，熟悉CI/CD流程及其在软件开发中的作用。",
            "5，熟悉Kubernetes及其在微服务部署和管理中的应用。",
            "6，有使用云服务（如腾讯云，阿里云，AWS, Azure, GCP）的经验。",
            "7，能够设计和实现RESTful API，并确保前后端分离架构的安全性和兼容性。",
          ]
        },
        {
          name: '技术要求',
          value: [
            "1，Java：深入了解Java语言，能够编写高效、可维护的代码。",
            "2，Kafka：配置topic、消息分区机制、性能优化和故障诊断。",
            "3，ZooKeeper：分布式系统角色、集群配置协调、leader故障影响。",
            "4，Nginx：服务器/反向代理选择、高并发处理策略、与后端应用配合。",
            "5，Spring：依赖注入、事务管理、Spring Security。",
            "6，Maven：项目生命周期、依赖管理、自动化构建和测试。",
            "7，CI/CD：流程配置、构建失败定位和解决。",
            "8，Kubernetes：核心组件、自动扩展、微服务部署和管理。",
            "9，云服务：云平台使用、成本性能影响、多云资源管理。",
            "10，接口开发：RESTful API设计、前后端分离安全性、性能问题解决。",
            "11，前后端分离：架构优点、实施挑战、接口兼容性和数据一致性。",
            "12，用户身份验证与授权：Keycloak、OIDC、MFA、认证流程优化。",
            "13，AI基础知识：GPT原理、Stable Diffusion原理、AI项目经验。",
            "14，项目管理经验：团队领导、项目协调、敏捷开发实践。",
          ]
        },
        {
          name: '加分项',
          value: [
            "1，熟悉Keycloak、OpenID Connect等身份验证和授权机制。",
            "2，了解AI技术，如GPT和Stable Diffusion，并有相关项目经验。",
            "3，具备项目管理经验，能够领导团队并确保项目按时交付。",
          ]
        }
      ]
    },
    {
      id: 3,
      title: 'UI设计师',
      content: [
        {
          name: '工作职责',
          value: [
            "1，设计和实现用户友好的界面，确保产品的易用性和美观性。",
            "2，进行用户研究，创建用户画像，以指导设计决策。",
            "3，与开发团队紧密合作，确保设计的有效实施。",
            "4，定期进行用户测试，收集反馈，并根据反馈优化设计。",
          ]
        },
        {
          name: '基本要求',
          value: [
            "1，熟练掌握UI/UX设计原则和最佳实践。",
            "2，精通Sketch、Adobe XD、Figma等设计工具。",
            "3，理解响应式设计和跨平台设计的重要性。",
            "4，熟悉用户研究方法，包括用户访谈、调查问卷和可用性测试。",
            "5，能够创建交互式原型和设计规范文档。",
            "6，了解前端开发基础，与开发团队有效沟通。",
          ]
        },
        {
          name: '技术要求',
          value: [
            "1，熟练使用Sketch、Adobe XD、Figma等设计工具，能够高效地创建高质量的设计稿。",
            "2，理解不同设备和屏幕尺寸的设计挑战，能够设计适应多种设备的界面。",
            "3，掌握用户研究方法，能够通过用户访谈、调查问卷和可用性测试来收集用户需求和反馈。",
            "4，能够使用工具如InVision、Marvel或Axure创建交互式原型。",
            "5，能够创建和维护设计系统和组件库，确保设计的一致性和可复用性。",
            "6，了解HTML、CSS和JavaScript，能够与开发团队有效沟通设计细节。",
            "7，深刻理解用户体验的重要性，能够设计出满足用户需求和期望的产品。",
            "8，具备良好的时间管理和项目协调能力，确保设计工作按时完成。",
            "9，能够在多学科团队中有效沟通和协作，推动项目进展。",
          ]
        },
        {
          name: '加分项',
          value: [
            "1，有成功的产品UI/UX设计案例。",
            "2，了解最新的UI/UX设计趋势和技术。",
            "3，具备良好的项目管理和团队协作能力。",
            "4，有跨文化设计经验，能够适应不同市场的需求。",
          ]
        },
        {
          name: '附加技能',
          value: [
            "1，了解动画和交互设计，能够为产品添加动态元素。",
            "2，具备品牌设计经验，能够维护和强化品牌形象。",
            "3，有数据分析能力，能够利用数据来指导设计决策。",
          ]
        },
        {
          name: '工作经验',
          value: [
            "1，至少2年以上的UI/UX设计经验，有可验证的作品集。",
            "2，有在敏捷开发环境中工作的经验。",
          ]
        },
        {
          name: '教育背景',
          value: [
            "1，相关领域的学士学位，如平面设计、交互设计、工业设计等。",
            "2，持续的自我学习和专业发展，保持对行业动态的敏感性。",
          ]
        }
      ]
    },
  ]

</script>

<template>
  <div class="join-us">
    <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="2s">加入我们</div>
    <div class="sub-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">广东爱简艺人工智能科技有限公司</div>
    <div class="sub-title-en wow animate__animated animate__fadeInDown" data-wow-duration="1s">Guangdong Easy Art Artificial Intelligence Tech.Co,Ltd</div>
    <div class="recruitment-information-content">
      <div class="list-title">招聘信息</div>
      <el-collapse v-model="activeNames" @change="handleChange" accordion>
        <el-collapse-item :title="item.title" :name="item.id" v-for="(item, index) in joinUsList" :key="index">
          <div v-for="(itm, ind) in item.content" :key="ind">
            <div class="list-item-title">{{ itm.name }}</div>
            <div class="list-item-content" v-for="(i, j) in itm.value" :key="j">{{ i }}</div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>



<style scoped lang="less">
  .join-us {
    width: 60%;
    margin: 0 auto;
    padding: 1rem 0 1rem 0;
    .big-title {
      margin-top: 0.6rem;
      margin-bottom: 0.3rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.44rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .sub-title {
      margin-bottom: 0.16rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.3rem;
      color: #FFFFFF;
      text-align: center;
      line-height: 0.28rem;
      font-style: normal;
      text-transform: none;
    }
    .sub-title-en {
      font-family: Noto Sans SC;
      font-size: 0.18rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
      line-height: 0.28rem;
      font-style: normal;
      text-transform: none;
    }
    .recruitment-information-content {
      min-height: 8.2rem;
      margin-top: 0.36rem;
      padding: 0.2rem 0.3rem;
      background: rgba(17,17,17,0.5);
      box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.3);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-image: linear-gradient(180deg, rgba(110, 110, 110, 0.3), rgba(45, 45, 45, 0.1)) 1 1;
      .list-title {
        font-family: Noto Sans SC;
        font-weight: bold;
        font-size: 0.24rem;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 0.06rem;
        margin-bottom: 0.23rem;
      }
      /deep/ .el-collapse {
        --el-collapse-header-bg-color: #222222;
        --el-collapse-header-text-color: #FFFFFF;
        --el-collapse-header-font-size: 0.22rem;
        --el-collapse-header-height: 0.69rem;
        --el-collapse-border-color: none;
        --el-collapse-content-bg-color: #222222;
        border-bottom: none;
        border-top: none;
        .el-collapse-item {
          .el-collapse-item__header {
            padding-left: 0.2rem;
            font-size: 0.24rem;
          }
        }
        .el-collapse-item__content {
          padding: 0.2rem;
          margin: 0px 0.2rem 0.2rem 0.2rem;
          background: #333;
          color: #fff;
        }
        
      }
      .list-item-title {
        font-family: Noto Sans SC;
        font-weight: 500;
        font-size: 0.22rem;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 0.12rem;
        
      }
      .list-item-content{
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.18rem;
        color: rgba(255,255,255,0.7);
        text-align: left;
        font-style: normal;
        text-transform: none;
        &:last-child {
          margin-bottom: 0.2rem;
        }
      }
    }
  }
</style>