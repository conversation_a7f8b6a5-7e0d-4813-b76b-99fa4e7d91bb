<template>
  <div class="login-warp">
    <el-dialog v-model="props.isShow" width="600" center :close-on-click-modal="false" :close-on-press-escape="false" @close="closeDialog" :show-close="false">
      <!-- <div class="login-content" id="login_container"></div> -->
      <div class="login-content">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          :hide-required-asterisk="true"
          class="demo-ruleForm"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="ruleForm.username" placeholder="请输入用户名"/>
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input v-model="ruleForm.password" :type="password === 'password' ? password : 'text'" placeholder="请输入密码">
              <template #suffix>
                <el-icon @click="showPassword">
                  <component :is="password === 'password' ? 'View' : 'Hide'"></component>
                </el-icon>
              </template>
            </el-input> 
          </el-form-item>

          <el-form-item label="验证码" prop="code">
            <el-row>
              <el-col :span="14">
                <el-input v-model="ruleForm.code" placeholder="请输入验证码"/>
              </el-col>
              <el-col :span="10">
                <img :src="captchaImg" alt="" class="captcha-img" @click="getCaptchaImage">
              </el-col>
            </el-row>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="login()">登 录</el-button>
          </el-form-item>

        </el-form>

      </div>
    </el-dialog>
  </div>
</template>


<script setup>
  import { ref, defineProps, defineEmits, reactive, onMounted } from 'vue';
  import axios from 'axios';
  const ruleFormRef = ref(null);
  const ruleForm = reactive({
    username: '',
    password: '',
    code: '',
    uuid: ''
  })
  const rules = reactive({
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
    ],
    code: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
    ]
  })

  const emit = defineEmits(['closeDialog']);

  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    }
  });

  const closeDialog = () => {
    emit('closeDialog');
  }

  const password = ref('password');
  const showPassword = () => {
    password.value = password.value === 'password' ? 'text' : 'password';
  }

  const captchaImg = ref('');
  const getCaptchaImage = () => {
    axios.get(`https://easyart.cc/prod-api/captchaImage`).then(({data}) => {
      if(data.code === 200) {
        ruleForm.uuid = data.uuid;
        captchaImg.value = 'data:image/*;base64,' + data.img;
      }
    }).catch((err) => {
      console.log(err)
    })
  }

  const login = () => {
    axios.post(`https://easyart.cc/prod-api/login`, ruleForm).then(({data}) => {
      if(data.code === 500) {
        getCaptchaImage()
        ElMessage.warning(data.msg)
        return;
      }
      if(data.code === 200) {
        localStorage.setItem('token', data.token)
        ElMessage.success('登录成功！')
        emit('closeDialog', data.token);
      }
    }).catch((err) => {
      console.log(err)
    })
  }

  onMounted(() => {
    getCaptchaImage()
  })

  /* watch(() => {
    if(props.isShow) {
      setTimeout(() => {
        var state = Math.random().toString().substring(2);
        let obj = new WxLogin({
          self_redirect:false,
          id:"login_container", 
          appid: "wx9b7621784d3874d9", 
          scope: "snsapi_login", 
          redirect_uri: "http://easyart.cc",
          state: state,
          style: "white",
          href: ""
        });
      }, 100);
    }
  }) */
  
</script>

<style scoped lang="less">
.login-content {
  display: flex;
  align-items: center;
  justify-content: center;
  /deep/ .el-form {
    .el-form-item {
      margin-bottom: 30px;
      .el-form-item__label-wrap {
        .el-form-item__label {
          color: #fff;
        }
      }
      .el-form-item__content {
        justify-content: center;
        .el-input {
          --el-input-height: 40px;
        }
        .el-button {
          text-align: center;
        }
        .el-row {
          .captcha-img {
            border-radius: 4px;
            height: 40px;
            cursor: pointer;
            margin-left: 10px;
            object-fit: cover;
          }
        }
      }
    }
  }
}
</style>

<style lang="less">
.login-warp {
  .el-overlay-dialog {
    .el-dialog--center {
      padding: 60px 20px 40px 20px;
    }
  }
}
</style>