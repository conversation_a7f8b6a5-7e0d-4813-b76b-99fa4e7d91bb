<template>
  <div class="cases-wrapper">
    <div class="cases-cy">
      <div class="page-bg2"></div>
      <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">
        AIGC+应用技术开发
      </div>
      <div class="case-cy-title wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
        爱简艺在数字产业领域，致力于运用人工智能技术解决行业实际问题，推动企业经营提质增效。通过AI+产业场景应用赋能企业，深入挖掘产业应用和企业需求，提供定制化的AI大模型研发和数据整合服务。同时，依托自主研发的AI获客系统和AI数字人等创新营销工具，我们为不同领域的企业、园区和楼宇运营商等提供高质量的发展解决方案及相应的软硬件配套支持。
      </div>
      <div class="case-cy">
        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">数字产业</div>
            <div class="desc">
              利用EasyArt
              AI赋能创意设计链条，包括产品模型定制、AI模特服饰上身、室内设计与建筑设计等。
            </div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" direction="vertical" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/fushi.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <div slot="name">
                  <div class="title">服饰上身</div>
                  <div class="desc">
                    服饰试款、服饰展示无需打板打样、更无需真实模特。用爱简艺AI操作，服饰上身效果直观感受。
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy1" alt="" class="img" />
                  <img :src="cy2" alt="" class="img" />
                </div>
                <div slot="name">
                  <div class="title">产品模型</div>
                  <div class="desc">
                    想直观查看产品效果图，建模入门难，技能水平要求高。用爱简艺AI制作，贴合应用场景清晰评估。
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy51" alt="" class="img2" />
                  <img :src="cy52" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">室内设计与建筑设计</div>
                  <div class="desc">
                    室内设计用3Dmaks、酷家乐或Su做效果图效率低。用爱简艺AI制作渲染，不同设计风格快速出图。
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container student">
            <el-carousel :interval="4000" height="5rem">
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">佛山日资Frictificer</div>
                  <div class="desc">赋能日资投资性企业员工结合实际场景 提高10X办公效率。</div>
                </div>
                <div class="items">
                  <img :src="cy12" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">广东省交通旅行社有限公司</div>
                  <div class="desc">AIGC实际场景应用员工培训。</div>
                </div>
                <div class="items">
                  <img :src="cy7" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">华为东莞松山湖</div>
                  <div class="desc">赋能企业数字化转型商业场景AI解决方案。</div>
                </div>
                <div class="items">
                  <img :src="cy8" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">顺德单向空间</div>
                  <div class="desc">赋能读书会成员了解使用AI工具，高效阅读。</div>
                </div>
                <div class="items">
                  <img :src="cy9" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">广州百赞国际软件（WNS）有限公司</div>
                  <div class="desc">办公软件与AIGC结合，赋能员工10倍生产力。</div>
                </div>
                <div class="items">
                  <img :src="cy10" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div slot="name">
                  <div class="title">上海撰喜商学院</div>
                  <div class="desc">赋能私域超高批量量产内容。</div>
                </div>
                <div class="items">
                  <img :src="cy11" alt="" class="img3" />
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div class="left-container">
            <div class="title">AI+企业应用实训</div>
            <div class="desc">
              为提升更多企业对AIGC应用的理解，掌握数字化技能，提高工作效率。爱简艺已为广东省交通旅行社、广州百赞国际软件（WNS）、佛山日资Frictificer、华为东莞松山湖等企业或单位开展AI应用实操沙龙和宣讲培训会达50+场。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI数字人</div>
            <div class="desc">
              打造企业ip+品牌IP，实现营销矩阵铺设。赋能产品溢价，实现自有多渠道销售。
            </div>
          </div>
          <div class="right-container">
            <video
              src="https://cdn.aieasyart.com/easyart/video/digitalPeople.mp4"
              controls="controls"
              autoplay
              muted
              loop
              class="img"
            ></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container">
            <video
              src="https://cdn.aieasyart.com/easyart/video/ai-huoke.mp4"
              controls="controls"
              autoplay
              muted
              loop
              class="img"
            ></video>
          </div>
          <div class="left-container">
            <div class="title">AI获客平台</div>
            <div class="desc">
              一键搞定内容制作传统步骤，助力“企业获客-降本增效-产业提质”，赋能产业领域创造更多新突破。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI+软件应用技术开发案例</div>
            <div class="desc">
              利用EasyArt
              AI赋能创意设计链条，包括产品模型定制、AI模特服饰上身、室内设计与建筑设计等。
            </div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" direction="vertical" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy1" alt="" class="img" />
                  <img :src="cy2" alt="" class="img" />
                </div>

                <div slot="name">
                  <div class="title">AI.EasyArt创意图像生成平台(To B)</div>
                  <div class="desc">
                    搭载EasyArt乌龙大模型的创意内容生成平台，通过大量优质的艺术家、艺术作品资源积累及投入训练，打造的创意内容大模型，搭载多形式的图像生成功能模块，通过针对特定企业的定制化开发及模型训练，满足企业在公司宣传、产品设计、产品宣传等方面的应用需求，提高公司平台视觉生成效率与质量。
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy1" alt="" class="img" />
                  <img :src="cy2" alt="" class="img" />
                </div>
                <div slot="name">
                  <div class="title">AI.EasyChat企业级Agent开发平台(To B)</div>
                  <div class="desc">
                    通过对多个大模型的本地部署与搭设企业级AI智能体开发平台，通过SQL知识库、RAG技术及MCP协议的加持，可针对特定行业场景应用智能体进行便捷搭建，并基于企业特定需求，进行定制化的智能体搭建开发，为企业内部共享应用提供落地实践。
                  </div>
                </div>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy51" alt="" class="img2" />
                  <img :src="cy52" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">AI智能群控微信客服平台(To B)</div>
                  <div class="desc">
                    通过AI智能体机器人和意图关键词的结合，快速识别私域用户的真实需求并进行精准回应。智能体根据意图识别和关键词匹配可以提供自定义回复，也能灵活处理其他未定义的问询，确保每次互动都能高效、准确地满足用户需求，通知支持客户标签自动化管理及群发控制。
                  </div>
                </div>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy51" alt="" class="img2" />
                  <img :src="cy52" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">连锁门店设计标准化模型(To B)</div>
                  <div class="desc">
                    通过定制化模型训练与节点搭建，为特定品牌的连锁设计落地提供人工智能赋能，加快连锁品牌经营商的扩张能力与落地能力，降低前期设计效果模力成本。
                  </div>
                </div>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="cy51" alt="" class="img2" />
                  <img :src="cy52" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">电商服装迁移模型（To B/C）</div>
                  <div class="desc">
                    通过定制化模型训练与节点搭建，为电商服装商家提供高一致性的本地化AI应用模型，从上衣、裤子到鞋子等涵盖大部分部位的迁移模型，大大提高产品出图效率，降低商家在产品图拍摄成本。
                  </div>
                </div>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <!-- <img :src="cy51" alt="" class="img2" />
                  <img :src="cy52" alt="" class="img2" /> -->
                </div>
                <div slot="name">
                  <div class="title">定制数字人对话平台(To C)</div>
                  <div class="desc">
                    定制指定人物数字人形象、TTS音色，链接定制化情感陪伴智能体，为指定人物提供互动式交互场景，即时聊天对话。
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>

          <div class="left-container">
            <div class="title">AI.EasyChat企业级Agent开发平台(To B)</div>
            <div class="desc">
              通过对多个大模型的本地部署与搭设企业级AI智能体开发平台，通过SQL知识库、RAG技术及MCP协议的加持，可针对特定行业场景应用智能体进行便捷搭建，并基于企业特定需求，进行定制化的智能体搭建开发，为企业内部共享应用提供落地实践。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI.EasyArt创意图像生成平台(To B)</div>
            <div class="desc">
              搭载EasyArt乌龙大模型的创意内容生成平台，通过大量优质的艺术家、艺术作品资源积累及投入训练，打造的创意内容大模型，搭载多形式的图像生成功能模块，通过针对特定企业的定制化开发及模型训练，满足企业在公司宣传、产品设计、产品宣传等方面的应用需求，提高公司平台视觉生成效率与质量。
            </div>
          </div>
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>

          <div class="left-container">
            <div class="title">AI智能群控微信客服平台（To B）</div>
            <div class="desc">
              通过AI智能体机器人和意图关键词的结合，快速识别私域用户的真实需求并进行精准回应。智能体根据意图识别和关键词匹配可以提供自定义回复，也能灵活处理其他未定义的问询，确保每次互动都能高效、准确地满足用户需求，通知支持客户标签自动化管理及群发控制。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">连锁门店设计标准化模型（To B）</div>
            <div class="desc">
              通过定制化模型训练与节点搭建，为特定品牌的连锁设计落地提供人工智能赋能，加快连锁品牌经营商的扩张能力与落地能力，降低前期设计效果模力成本。
            </div>
          </div>
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>

          <div class="left-container">
            <div class="title">电商服装迁移模型（To B/C）</div>
            <div class="desc">
              通过定制化模型训练与节点搭建，为电商服装商家提供高一致性的本地化AI应用模型，从上衣、裤子到鞋子等涵盖大部分部位的迁移模型，大大提高产品出图效率，降低商家在产品图拍摄成本。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">定制数字人对话平台(To C)</div>
            <div class="desc">
              定制指定人物数字人形象、TTS音色，链接定制化情感陪伴智能体，为指定人物提供互动式交互场景，即时聊天对话。
            </div>
          </div>
          <div class="right-container">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

      </div>
    </div>
    <!-- <div class="case-anli">
      <div class="page-bg"></div>
      <div class="case-cy-title">合作案例</div>
      <div class="case-cy-sub-title">为多个企业提供专属AI大模型研发及数据整合服务，并依托自研AI获客系统、AI数字人等新质营销工具，为各领域企业、园区及楼宇运营商等主体的经营提供高质量发展解决方案及软硬件配套。</div>
      <div class="swiper-container">
        <InfiniteScroll  :content="caseCyList" :direction="left">
          <template v-slot="{ item}">
            <div v-if="item.images && item.images.length === 1" class="iamge1">
              <img :src="item.images[0].name" alt="image" class="image" />
            </div>
            <div v-if="item.images && item.images.length === 2"  class="iamge2">
              <img :src="ite.name" alt="" v-for="(ite, index) in item.images" :key="index" class="image" />
            </div>
            <div v-if="item.images && item.images.length === 3"  class="iamge3">
              <img :src="ite.name" alt="" v-for="(ite, index) in item.images" :key="index" class="image" />
            </div>
            <div class="loop-title">
              {{item.subTitle}}
            </div>
            <div class="loop-sub-title">
              {{item.text}}
            </div>
          </template>
        </InfiniteScroll>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import InfiniteScroll from '@/components/InfiniteScroll.vue'
import cy1 from '@/assets/case/chanye/cy1.jpg'
import cy2 from '@/assets/case/chanye/cy2.jpg'
import cy41 from '@/assets/case/chanye/cy4-1.png'
import cy42 from '@/assets/case/chanye/cy4-2.png'
import cy51 from '@/assets/case/chanye/cy5-1.png'
import cy52 from '@/assets/case/chanye/cy5-2.png'
import cy6 from '@/assets/case/chanye/cy6.jpg'
import cy7 from '@/assets/case/chanye/cy7.jpg'
import cy8 from '@/assets/case/chanye/cy8.jpg'
import cy9 from '@/assets/case/chanye/cy9.jpg'
import cy10 from '@/assets/case/chanye/cy10.jpg'
import cy11 from '@/assets/case/chanye/cy11.jpg'
import cy12 from '@/assets/case/chanye/cy12.jpg'

const caseCyList = [
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '佛山日资Frictificer丨赋能日资投资性企业员工结合实际场景 提高10X办公效率',
    text: '',
    showType: 'line',
    images: [{ id: 0, name: cy12 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '爱简艺AI营销数字人',
    text: '打造企业ip+品牌IP，实现营销矩阵铺设。赋能产品溢价，实现自有多渠道销售。',
    images: [{ id: 0, name: cy6 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '广东省交通旅行社有限公司丨AIGC实际场景应用员工培训',
    text: '',
    images: [{ id: 0, name: cy7 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '华为东莞松山湖丨赋能企业数字化转型商业场景AI解决方案',
    text: '',
    images: [{ id: 0, name: cy8 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '单向空间顺德丨赋能读书会成员了解使用AI工具，高效阅读',
    text: '',
    images: [{ id: 0, name: cy9 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '广州百赞国际软件（WNS）有限公司丨办公软件与AIGC结合，赋能员工10倍生产力',
    text: '',
    images: [{ id: 0, name: cy10 }]
  },
  {
    key: 'cy',
    title: '数字产业板块',
    subTitle: '上海撰喜商学丨赋能私域超高批量量产内容',
    text: '',
    images: [{ id: 0, name: cy11 }]
  }
]
</script>

<style lang="less" scoped>
.cases-wrapper {
  width: 100%;
  margin: 0 auto;
  padding: 1rem 0 0 0;
  .cases-cy {
    width: 100%;
    position: relative;
    padding-bottom: 0.8rem;
    .page-bg2 {
      background-image: url('@/assets/bg/bg7.jpg');
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.4;
      z-index: -1;
    }
    .big-title {
      width: 60%;
      margin: 0 auto;
      padding-top: 60px;
      margin-bottom: 0.5rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .case-cy-title {
      width: 60%;
      margin: 0 auto;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.26rem;
      color: rgba(255, 255, 255, 1);
      text-align: left;
      letter-spacing: 0.02rem;
      margin-bottom: 1rem;
    }
    .case-cy {
      width: 60%;
      margin: 0 auto;
      .case-cy-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        .left-container {
          width: 40%;
          .title {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.32rem;
            color: #71deb6;
            text-align: left;
            margin-bottom: 0.2rem;
          }
          .desc {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.24rem;
            color: rgba(255, 255, 255, 1);
            text-align: left;
          }
        }
        .right-container {
          width: 53%;
          padding: 0.3rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.1rem;
          background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
          .img {
            width: 100%;
            height: 4.2rem;
            object-fit: cover;
            border-radius: 0.1rem;
          }
        }
        .select-container {
          width: 57%;
          padding: 0.3rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.1rem;
          background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
          /deep/ .el-carousel {
            width: 100%;
            .el-carousel__container {
              .items {
                display: flex;
                .img {
                  width: 45%;
                  border-radius: 0.1rem;
                  margin-right: 0.2rem;
                  &:last-child {
                    margin-right: 0;
                  }
                  object-fit: cover;
                }
                .video {
                  width: 92.33333%;
                  height: 3.5rem;
                  background: #000;
                  border-radius: 0.1rem;
                }
                .img2 {
                  width: 45%;
                  height: 3.53rem;
                  margin-right: 0.2rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                }
                .img3 {
                  width: 100%;
                  height: 4rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                }
              }
              .title {
                font-family: Noto Sans SC;
                font-weight: 600;
                font-size: 0.3rem;
                color: #000;
                text-align: left;
                margin-top: 0.2rem;
              }
              .desc {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.22rem;
                color: #000;
                text-align: left;
                width: 93%;
                margin-top: 0.08rem;
              }
            }
            .el-carousel__indicators {
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.4rem;
                  width: 0.03rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  180deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
        .student {
          /deep/ .el-carousel {
            width: 100%;
            .el-carousel__container {
              .title {
                margin-top: 0;
              }
              .desc {
                margin-bottom: 0.2rem;
              }
            }
            .el-carousel__indicators {
              bottom: -10px;
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.03rem;
                  width: 0.4rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  90deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
      }
      .case-cy-item1 {
        flex-direction: column;
        .left-container {
          width: 100%;
          .title {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.32rem;
            color: #71deb6;
            text-align: center;
            margin-bottom: 0.2rem;
          }
          .desc {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.24rem;
            color: rgba(255, 255, 255, 1);
            text-align: center;
            margin-bottom: 0.3rem;
          }
        }
        .select-container {
          width: 100%;
          /deep/ .el-carousel {
            .el-carousel__container {
              .items {
                display: flex;
                margin-top: 1rem;
                .img {
                  width: 48%;
                  border-radius: 0.1rem;
                  margin-right: 0.2rem;
                  &:last-child {
                    margin-right: 0;
                  }
                  object-fit: cover;
                }
                .video {
                  width: 100%;
                  height: 3.5rem;
                  border-radius: 0.1rem;
                }
                .img2 {
                  width: 50%;
                  height: 3.53rem;
                  margin-right: 0.2rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                }
              }
              .title {
                font-family: Noto Sans SC;
                font-weight: 600;
                font-size: 0.32rem;
                color: #000;
                text-align: center;
                margin-top: 0.2rem;
              }
              .desc {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.22rem;
                color: #000;
                text-align: center;
              }
            }
            .el-carousel__indicators {
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.04rem;
                  width: 0.6rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background-color: #2952ff;
              }
            }
          }
        }
      }
    }
  }
  /* .case-anli {
      width: 100%;
      position: relative;
      padding-top: 3rem;
      padding-bottom: 3rem;
      .page-bg {
        background-image: url('@/assets/image/home-bg.jpg');
        background-size: 100% 50%;
        background-repeat: no-repeat;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0.4;
        z-index: -1;
      }
      .case-cy-title {
        width: 60%;
        margin: 0 auto;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.5rem;
        color: #fff;
        text-align: left;
        letter-spacing: 1px;
        margin-bottom: 0.4rem;
      }
      .case-cy-sub-title {
        width: 60%;
        margin: 0 auto;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.35rem;
        color: rgba(255, 255, 255, 0.7);
        text-align: left;
        letter-spacing: 1px;
        margin-bottom: 0.6rem;
      }
      .swiper-container {
        margin-top: 0.6rem;
        width: 100%;
        height: 5rem;
      }
    } */
}
.page-bg {
  background-image: url('@/assets/image/home-bg6.jpg');
  background-size: 100% 50%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.4;
  z-index: -1;
}
</style>
