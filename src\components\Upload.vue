<template>
  <el-upload class="upload-warp" 
    drag 
    :limit="1"
    action="https://www.easyart.cc/prod-api/mj/img2img" 
    :auto-upload="false" 
    accept="image/*" 
    ref="uploadRef" 
    :on-change="handleChange"
    :on-success="handleSuccess"
    :on-remove="handleRemove"
    :data="uploadParams"
    list-type="picture-card"
  >
    <img :src="uploadIcon" alt="" class="upload-icon">
    <div class="el-upload__text">
      拖拽或浏览上传图片
    </div>
  </el-upload>
</template>

<script setup lang="ts">
  import uploadIcon from '@/assets/image/uploadIcon.png'
  import { ref, defineEmits, defineExpose, defineProps, watch } from 'vue'
  import type { UploadInstance } from 'element-plus'
  const uploadRef = ref<UploadInstance>()
  let props = defineProps(['inputValue'])
  const fileInfo = ref(null)
  const uploadParams = ref({
    file: fileInfo.value,
    prompt: props.inputValue,
    action: 'generate'
  })
  const emit = defineEmits(['uploadSuccess', 'updateImageInfo']);
  defineExpose({
    submitUpload() {
      uploadParams.value.prompt = props.inputValue
      uploadParams.value.file = fileInfo.value
      console.log(uploadParams.value)
      uploadRef.value!.submit()
    }
  })

  const handleSuccess = (res: any) => {
    emit('uploadSuccess', res)
  }

  const handleRemove = () => {
    emit('updateImageInfo', false)
  }

  const handleChange = (file: any, fileList: any) => {
    fileInfo.value = file
    emit('updateImageInfo', true)
  }

</script>

<style lang="less" scoped>
  .upload-warp {
    width: 50%;
  }
  /deep/ .el-upload-list--picture-card {
    position: relative;
    width: 100%;
    --el-upload-list-picture-card-size: 100%;
    .el-upload-list__item {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 80;
      width: 100%;
      height: 2.7rem;
      background: rgba(0, 0, 0, 0.7);
      border: 1px dashed rgba(255,255,255,0.3);
    }
    .el-upload-list__item-thumbnail {
      object-fit: cover;
    }
    .el-upload {
      width: 100%;
      height: 2.7rem;
      background: none;
      border: none;
      .el-upload-dragger {
        background: rgba(0, 0, 0, 0.7);
        padding: 0px;
        // border: 1px dashed rgba(255,255,255,0.3);
        border: 1px dashed #fff;
        height: 2.7rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:hover {
          // border: 1px dashed #212224;
          border: 1px dashed #fff;
        }
        .el-upload__text {
          color: #fff;
          margin-top: 0.06rem;
        }
      }
    }
    
  }
  
</style>