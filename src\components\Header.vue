
<template>
  <div class="header-warp" ref="headerRef" :class="{ 'header-warp-bg': isFixed, 'show-header': isUp , 'hide-header': !isUp }">
    <div class="header-content">
      <div class="logo-warp" @click="router.push('/')">
        <img src="@/assets/image/logo1.png" alt="logo-img" class="logo-img" />
        <div class="logo-text"><span class="bold">Easy Art</span> 爱简艺</div>
      </div>
      <div class="nav-warp">
        <div v-if="!isShowtTeachingExamHeader">
          <el-menu ellipsis class="el-menu-popper-demo" mode="horizontal" :popper-offset="16" @select="selectMenu">
            <div v-for="(item, index) in menuList" :key="index" >
              <div v-if="item.children">
                <el-sub-menu :index="item.id" >
                  <template #title>{{item.name}}</template>
                  <el-menu-item :index="itm.id" v-for="(itm, ind) in item.children" :key="ind">
                    {{itm.name}}
                  </el-menu-item>
                </el-sub-menu>
              </div>
              <div v-else>
                <el-menu-item :index="item.id">
                    <div v-if="item.id === '#cases'">
                      <a href="#cases" class="anchor" >{{item.name}}</a>
                    </div>
                    <div v-else> 
                    {{item.name}}
                  </div>
                </el-menu-item>
              </div>
            </div>
          </el-menu>
        </div>
        <div v-if="isShowtTeachingExamHeader" class="teachingExam-header">
          <el-menu ellipsis class="el-menu-popper-demo" mode="horizontal" :popper-offset="16" @select="selectTMenu" :default-active="defaultActive">
            <el-menu-item index="/teachingExam">AIGC课程</el-menu-item>
            <el-menu-item index="/teachingExam/echarts">学习统计</el-menu-item>
          </el-menu>
        </div>
      </div>
      <div class="user-warp" v-if="isShowBtn">
        <div @click="router.push('/collaborationConsultation')" class="cooperation">合作咨询</div>
      </div>
    </div>
    
  </div>
</template>

<script setup name="Header">
  import {reactive, ref, onMounted, watch} from 'vue';
  import { useRoute, useRouter } from 'vue-router'
  const router = useRouter()
  const route = useRoute()
  console.log(route.name)
  const headerRef = ref(null)
  const isFixed = ref(false)
  const isUp = ref(true)
  
  
  let prevScrollPos = 0; // 初始滚动位置
  
  onMounted(() => {
    window.addEventListener('scroll', handleScroll)
  })

  const handleScroll = () => {
    if (window.scrollY > 100) {
      isFixed.value = true
    } else {
      isFixed.value = false
    }
    // 判断滚动条向上还是向下
    if (window.scrollY > prevScrollPos) {
      // 向下滚动
      isUp.value = false
    } else {
      // 向上滚动
      isUp.value = true
    }
    prevScrollPos = window.scrollY;
  }

  const menuList = reactive([
    {
      id: '1',
      name: 'AIGC产品',
      children: [
        {
          id: 'EasyArt',
          name: 'AI设计平台'
        },
        {
          id: 'TeachingExam',
          name: 'AI教育平台'
        },
      ],
    },
    /* {
      id: '#cases',
      name: '服务案例',
    }, */
    {
      id: '2',
      name: '服务与案例',
      children: [
        {
          id: '/digitalIndustry',
          name: '数字产业'
        },
        {
          id: '/digitalEducation',
          name: '数字教育'
        },
        {
          id: '/digitalArt',
          name: '数字艺术'
        },
      ],
    },
    {
      id: '3',
      name: '关于我们',
      children: [
        {
          id: '/companyProfile',
          name: '公司简介'
        },
        {
          id: '/joinUs',
          name: '加入我们'
        },
      ],
    },
    {
      id: '/artCommunity',
      name: 'AI艺术社区',
    }
  ])
  let isShowtTeachingExamHeader = ref(false)
  let defaultActive = ref('/teachingExam')
  let isShowBtn = ref(true)
  watch(() => route.path, (newVal) => {
    if(newVal.includes('teachingExam')) {
      isShowBtn.value = false
      defaultActive.value = newVal
      isShowtTeachingExamHeader.value = true
    } else {
      isShowBtn.value = true
      isShowtTeachingExamHeader.value = false
    }
  })
  // 点击菜单
  const selectMenu = (key, keyPath) => {
    if(key === 'EasyArt') {
      window.open('https://aieasyart.com/', '_target')
    } else if(key === 'TeachingExam') {
      window.open('/teachingExam', '_blank')
    } else if (key==='#cases') {
      router.push('/#cases')
      return
    } else {
      router.push(key)
    }
  }
  const selectTMenu = (key, keyPath) => {
    router.push(key)
  }

</script>

<style lang="less" scoped>
  .header-warp {
    width: 100%;
    margin: 0 auto;
    height: 1rem;
    line-height: 0.73rem;
    display: flex;
    align-items: center;
    z-index: 1001;
    position: sticky;
    left: 0;
    top: 0;
    background: linear-gradient(rgba(0, 0, 0, .6), transparent) !important;
    .header-content {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      width: var(--width-header-value);
      .logo-warp {
        cursor: pointer;
        height: 0.65rem;
        min-width: 200px;
        margin-right: 0.4rem;
        display: flex;
        align-items: center;
        .logo-img {
          width: 0.7rem;
          height: 0.6rem;
        }
        .logo-text {
          font-size: 0.26rem;
          color: #FFFFFF;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-left: 0.1rem;
          letter-spacing: 1px;
          .bold {
            font-weight: bold;
          }
        }
      }
      .nav-warp {
        flex: 1;
        .el-menu {
          background: none !important;
          border-bottom: none;
          .el-menu-item {
            color: #fff;
            font-size: 0.26rem;
            &:hover {
              background: none !important;
            }
          }
          /deep/ .el-sub-menu {
            .el-sub-menu__title {
              color: #fff;
              font-size: 0.26rem;
              &:hover {
                background: none !important;
              }
            }
            .el-sub-menu__icon-arrow {
              font-size: 0.26rem;
              right: 5px;
              top: 45%;
            }
          }
          .anchor {
            font-size: 0.26rem;
            color: #fff;
            text-decoration: none;
          }
        }
      }
      .user-warp {
        background-image: linear-gradient(to right, #56CCF2 0%, #2F80ED  51%, #56CCF2  100%);
        transition: 0.5s;
        background-size: 200% auto;
        border-radius: 0.5rem;
        color: rgb(255, 255, 255);
        min-width: 1.2rem;
        height: 0.5rem;
        line-height: 0.5rem;
        padding: 0px 0.3rem;
        font-size: 0.2rem;
        cursor: pointer;
        text-align: center;
        &:hover {
          background-position: right center;
          color: #fff;
          text-decoration: none;
        }
      }
    }
  }
  .header-warp-bg {
    background: rgba(0, 0, 0, 0.5) !important;
  }
  .show-header {
    transform: translate(0);
    opacity: 1;
    animation: header-warp-show 0.2s ease-out both;
  }
  .hide-header {
    transform: translate(-100%);
    opacity: 0;
    animation: header-warp-hide 0.2s ease-out both;
  }
  @keyframes header-warp-hide {
    0% {
      transform: translateY(0);
      opacity: 1;
    }
    100% {
      transform: translateY(-100%);
      opacity: 0;
    }
  }
  @keyframes header-warp-show {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
</style>
