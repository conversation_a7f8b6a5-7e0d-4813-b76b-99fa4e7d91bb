<template>
  <div class="cases-wrapper">
    <div class="cases-jy">
      <div class="page-bg2"></div>
      <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">
        数字教育
      </div>
      <div class="case-cy-title wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
        爱简艺在数字教育领域，为高等院校提供全球领先的GPU、服务器部署及硬件设备、国内领先的AI教学实训平台、人工智能师资及学生课程培训、职业技能培训及源于AI产业实践的实训案例等校企合作服务项目。目前与南方科技大学、广东财经大学等国内知名高校共建多维度、强实操的AI+产业课程，为各类高校提供产教融合解决方案及人才就业扶持。
      </div>
      <div class="case-cy">
        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI+产教融合基地搭建</div>
            <div class="desc">
              通过与广州铁路职业技术学院、韩山师范学院等高校共建产教融合基地，为高校待毕业的应届生提供“AIGC理论培训+项目实操+就业上岗”的全链路服务项目。进行跨学科融合，持续推动AI产业与教育的深度结合。
            </div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy1" alt="" class="img3" />
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy4" alt="" class="img3" />
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5.5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/yingxin.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <h3 slot="name" class="title">
                  <div class="title">AIGC迎新特别活动——广东省技师学院</div>
                  <div class="desc">
                    今年，爱简艺利用自研EasyArt
                    AI生图平台，在广东省技师学院迎新日举办了一场独特的AI生图迎新活动，让全校师生在线下亲身体验人工智能生成图像技术的奇幻和独特的科技魅力。
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items center">
                  <img :src="jy5" alt="" class="img1" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人工智能课程共建——广东财经大学</div>
                  <div class="desc">
                    爱简艺携手广东财经大学艺术与设计学院，共同成立了一个专注于AI+课程共建的研发小组，开发一系列涵盖AI+数字艺术、AI+设计、AI+视频制作以及AI+PPT等多个融合人工智能技术的课程。
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy9" alt="" class="img" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">AI科普培训/宣讲会——华南理工大学</div>
                  <div class="desc">
                    爱简艺受邀到华南理工大学开展一场AIGC+校园宣讲会。主要通过向学校学生及老师传递人工智能相关的前沿知识，帮助他们掌握AIGC相关实际应用场景的知识与技能，并与现场的老师同学共探AI的无限可能性。
                  </div>
                </h3>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div class="left-container">
            <div class="title">AI+培训宣讲/活动策划</div>
            <div class="desc">
              爱简艺以自身的AI技术与各高校教育资源进行结合运用，与学校共建一系列关于AI+课程共建、AI+科普宣讲/培训以及AI+校园特别活动等项目。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI实训室/展厅搭建</div>
            <div class="desc">
              爱简艺在人工智能实训室/展厅建设上依托高校科研与教学资源优势，通过自主研发软硬件设备；整合教学资源平台，打造具有现代特色的人工智能实训室/展厅，满足多场景、一站式AI开发教学与实训需求。
            </div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" height="5.5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jyhy1" alt="" class="img2" />
                  <img :src="jyhy2" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人工智能实训室搭建——河源龙川教师发展中心</div>
                  <div class="desc">
                    搭建人工智能教育研究基地，打造集教育研究和实训为一体的人工智能教育研究基地，满足20-30人左右的科研小组的日常教学和研究工作。
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jynk1" alt="" class="img2" />
                  <img :src="jynk2" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人工智能展厅搭建——深圳南方科技大学未来教育中心</div>
                  <div class="desc">
                    通过AI技术设计并搭建富有科技感的空间，提供一体化的数字艺术与人工智能科技展示体验，利用智能设备带来先进方便的功能。
                  </div>
                </h3>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5.5rem">
              <!-- <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/yingxin.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <h3 slot="name" class="title">
                  <div class="title">AIGC迎新特别活动——广东省技师学院</div>
                  <div class="desc">
                    今年，爱简艺利用自研EasyArt
                    AI生图平台，在广东省技师学院迎新日举办了一场独特的AI生图迎新活动，让全校师生在线下亲身体验人工智能生成图像技术的奇幻和独特的科技魅力。
                  </div>
                </h3>
              </el-carousel-item> -->

              <!-- <el-carousel-item class="card-item">
                <div class="items center">
                  <img :src="jy5" alt="" class="img1" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">南方科技大学&爱简艺-人工智能教育研究基地</div>
                  <div class="desc">
                    爱简艺携手广东财经大学艺术与设计学院，共同成立了一个专注于AI+课程共建的研发小组，开发一系列涵盖AI+数字艺术、AI+设计、AI+视频制作以及AI+PPT等多个融合人工智能技术的课程。
                  </div>
                </h3>
              </el-carousel-item> -->

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy9" alt="" class="img" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">南方科技大学&爱简艺-人工智能教育研究基地</div>
                  <div class="desc">
                    爱简艺与南方科技大学未来教育研究中心在河源龙川教师发展中心合作搭建了人工智能教育研究基地，为龙川的科技教育创新与新质生产力培养带来了更多发展机遇，在“AI+教育”领域取得了进一步的突破。
                  </div>
                </h3>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy9" alt="" class="img" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">联合国教科文组织AIGC人才培养合作单位</div>
                  <div class="desc">
                    联合国教科文组织与爱简艺达成在AIGC+人才培养方向的深度合作，双方将在AI产品创新研发方面加大投入，共同探索人工智能在教育场景中的深度应用，开发更贴合教育需求的智能产品。
                  </div>
                </h3>
              </el-carousel-item>

            </el-carousel>
          </div>
          <div class="left-container">
            <div class="title">南方科技大学&爱简艺-人工智能教育研究基地</div>
            <div class="desc">
              爱简艺与南方科技大学未来教育研究中心在河源龙川教师发展中心合作搭建了人工智能教育研究基地，为龙川的科技教育创新与新质生产力培养带来了更多发展机遇，在“AI+教育”领域取得了进一步的突破。龙川人工智能教育研究基地建设理念主要是以教学、研发双重属性集合为中心，旨在打造一个能为教师提供融合最新AI技术和科技教育理念的学习环境，并提升教师对现代新教学模式的认知与理解，培养教师使用AI辅助教学的能力，实现推动AI在教育、科研和社会应用与发展的终极目标。
            </div>
          </div>
        </div>

         <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title"> 联合国教科文组织AIGC人才培养合作单位</div>
            <div class="desc">
联合国教科文组织与爱简艺达成在AIGC+人才培养方向的深度合作，双方将在AI产品创新研发方面加大投入，共同探索人工智能在教育场景中的深度应用，开发更贴合教育需求的智能产品。在教育培训体系建设领域，双方将整合资源，开展多层次、多形式的培训项目，提升各行业工作者运用AI技术的能力，为各领域学生提供更具前瞻性的AI教育课程。            </div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <!-- <img :src="jy1" alt="" class="img3" /> -->
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <!-- <img :src="jy4" alt="" class="img3" /> -->
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

<div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5.5rem">

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy9" alt="" class="img" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">南方科技大学&爱简艺-人工智能教育研究基地</div>
                  <div class="desc">
                    爱简艺与南方科技大学未来教育研究中心在河源龙川教师发展中心合作搭建了人工智能教育研究基地，为龙川的科技教育创新与新质生产力培养带来了更多发展机遇，在“AI+教育”领域取得了进一步的突破。
                  </div>
                </h3>
              </el-carousel-item>

              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="jy9" alt="" class="img" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">联合国教科文组织AIGC人才培养合作单位</div>
                  <div class="desc">
                    联合国教科文组织与爱简艺达成在AIGC+人才培养方向的深度合作，双方将在AI产品创新研发方面加大投入，共同探索人工智能在教育场景中的深度应用，开发更贴合教育需求的智能产品。
                  </div>
                </h3>
              </el-carousel-item>

            </el-carousel>
          </div>
          <div class="left-container">
            <div class="title">课程案例</div>
            <div class="desc">
              爱简艺为各地科技教育创新与新质生产力培养带来了更多发展机遇，在“AI+教育”领域取得了进一步的突破旨在打造一个能为对AI爱好者提供融合最新AI技术和科技教育理念的学习环境，培养使用AI辅助教学的能力，实现推动AI在教育、科研和社会应用与发展的终极目标。
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import jy1 from '@/assets/case/jiaoyu/jy1.jpg'
import jy4 from '@/assets/case/jiaoyu/jy4.jpg'
import jy5 from '@/assets/case/jiaoyu/jy5.jpg'
import jy9 from '@/assets/case/jiaoyu/jy9.jpg'
import jyhy1 from '@/assets/case/jiaoyu/jy-hy1.jpg'
import jyhy2 from '@/assets/case/jiaoyu/jy-hy2.jpg'
import jynk1 from '@/assets/case/jiaoyu/jy-nk1.jpg'
import jynk2 from '@/assets/case/jiaoyu/jy-nk2.jpg'
</script>

<style lang="less" scoped>
.cases-wrapper {
  width: 100%;
  margin: 0 auto;
  padding: 1rem 0 0 0;
  .cases-jy {
    width: 100%;
    position: relative;
    padding-bottom: 0.3rem;
    .page-bg2 {
      background-image: url('@/assets/bg/bg7.jpg');
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.4;
      z-index: -1;
    }
    .big-title {
      width: 60%;
      margin: 0 auto;
      padding-top: 60px;
      margin-bottom: 0.5rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .case-cy-title {
      width: 60%;
      margin: 0 auto;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.26rem;
      color: rgba(255, 255, 255, 1);
      text-align: left;
      letter-spacing: 0.02rem;
      margin-bottom: 1rem;
    }
    .case-cy {
      width: 60%;
      margin: 0 auto;
      .case-cy-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        .left-container {
          width: 40%;
          .title {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.32rem;
            color: #71deb6;
            text-align: left;
            margin-bottom: 0.2rem;
          }
          .title2 {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.28rem;
            color: #71deb6;
            text-align: left;
            margin-bottom: 0.2rem;
          }
          .desc {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.24rem;
            color: rgba(255, 255, 255, 1);
            text-align: left;
          }
        }
        .select-container {
          width: 57%;
          padding: 0.3rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.1rem;
          background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
          /deep/ .el-carousel {
            width: 100%;
            .el-carousel__container {
              .items {
                display: flex;
                .img {
                  width: 95%;
                  height: 3.6rem;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                  object-fit: cover;
                }
                .img1 {
                  height: 3.76rem;
                  text-align: center;
                  border-radius: 0.1rem;
                }
                .video {
                  width: 95%;
                  height: 3.76rem;
                  border-radius: 0.1rem;
                  background: #000;
                }
                .img2 {
                  width: 49%;
                  height: 3.75rem;
                  margin-right: 0.2rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                }
                .img3 {
                  width: 100%;
                  height: 5rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                }
              }
              .center {
                align-items: center;
                justify-content: center;
              }
              .title {
                font-family: Noto Sans SC;
                font-weight: 600;
                font-size: 0.3rem;
                color: #000;
                text-align: left;
                margin-top: 0.2rem;
              }
              .desc {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.22rem;
                color: #000;
                text-align: left;
                width: 93%;
                margin-top: 0.08rem;
              }
            }
            .el-carousel__indicators {
              bottom: -0.18rem;
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.03rem;
                  width: 0.4rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  90deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
        .select-container2 {
          /deep/ .el-carousel {
            .el-carousel__indicators {
              right: -0.12rem;
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.4rem;
                  width: 0.03rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  180deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
      }
    }
  }
}
</style>
