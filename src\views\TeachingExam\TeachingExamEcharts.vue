
<template>
  <div class="teaching-exam-echarts">
    <!-- <div class="teaching-exam-btns">
      <div :class="tabIndex == '1' ? 'teaching-exam-btn-item bgColor' : 'teaching-exam-btn-item'" @click="changeTab('1')">课程</div>
      <div :class="tabIndex == '2' ? 'teaching-exam-btn-item bgColor marginLeft24' : 'teaching-exam-btn-item marginLeft24'" @click="changeTab('2')">讲义</div>
    </div> -->
    <div class="teaching-exam-content">
      <div class="teaching-exam-content-left">
        <div class="title">数据概括</div>
        <div class="content">
          <div class="content-item" v-for="(item, idnex) in contentList" :key="index">
            <div class="content-item-num">{{item.name}}</div>
            <div class="content-item-title">{{item.num}}</div>
          </div>
        </div>
        <div class="quantityAnswersNumber">
          <div class="title">答题量</div>
          <div class="quantityAnswersNumber-content" id="quantityAnswersNumberId"></div>
        </div>
        <div class="quantityAnswersNumber">
          <div class="title">课程学习时长(min)</div>
          <div class="quantityAnswersNumber-content" id="quantityAnswersNumberId2"></div>
        </div>
        <div class="quantityAnswersNumber">
          <div class="title">综合量</div>
          <div class="quantityAnswersNumber-content" id="quantityAnswersNumberId3"></div>
        </div>
        <div class="certificateHonor">
          <div class="title">荣誉证书</div>
          <div class="certificateHonor-content">
            <div class="honor-item" v-for="(item, index) in honorList" :key="index">
              <img :src="item.url" alt="" class="honor-item-img">
            </div>
          </div>
        </div>
      </div>
      <StudentInfo />
    </div>
  </div>
  
</template>

<script setup>
  import StudentInfo from '@/components/StudentInfo.vue';
  import * as echarts from 'echarts';
  import { ref, onMounted } from 'vue';
  import honor from '@/assets/image/honor.png';
  import honor1 from '@/assets/image/honor1.png';

  let tabIndex = ref('1');
  const changeTab = (index) => {
    tabIndex.value = index;
  }

  const honorList = [
    {
      name: '优秀学员',
      url: honor
    },
    {
      name: '优秀学员',
      url: honor1
    },
    {
      name: '优秀学员',
      url: honor
    },
    {
      name: '优秀学员',
      url: honor1
    },
    {
      name: '优秀学员',
      url: honor
    },
    {
      name: '优秀学员',
      url: honor1
    },
  ]

  const contentList =  [
    {
      name: '测试题',
      num: 168
    },
    {
      name: '课时量',
      num: 240
    },
    {
      name: '自由学时量',
      num: 68
    },
  ]

  onMounted(() => {
    initQuantityAnswersNumber();
    initQuantityAnswersNumber2();
    initQuantityAnswersNumber3();
  })

  const initQuantityAnswersNumber = () => {
    const myChart = echarts.init(document.getElementById('quantityAnswersNumberId'));
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['今日答题量', '昨日答题量'],
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      yAxis: {
        splitLine: { 
          lineStyle: {
            type: 'dashed',
            color: '#666'
          }
        },
        type: 'value'
      },
      xAxis: {
        type: 'category',
        data: ['题库一', '题库二', '题库三', '题库四', '题库五', '题库六', '题库七']
      },
      series: [
        {
          name: '今日答题量',
          type: 'bar',
          stack: '总量',
          barWidth: 5,
          itemStyle: {
            color: '#00A389',
          },
          data: [1, 2, 5, 6, 2, 1, 5]
        },
        {
          name: '昨日答题量',
          type: 'bar',
          stack: '总量',
          barWidth: 5,
          itemStyle: {
            color: '#FF5B5B',
            borderRadius: [5, 5, 0, 0]
          },
          data: [2, 3, 3, 5, 1, 4, 2]
        },
        
      ]
    };
    myChart.setOption(option);
  }
  const initQuantityAnswersNumber2 = () => {
    const myChart = echarts.init(document.getElementById('quantityAnswersNumberId2'));
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['今日课程学习时长', '昨日课程学习时长'],
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['课程一', '课程二', '课程三', '课程四', '课程五', '课程六', '课程七']
      },
      yAxis: {
        type: 'value',
        splitLine: { 
          lineStyle: {
            type: 'dashed',
            color: '#666'
          }
        },
      },
      series: [
        {
          name: '今日课程学习时长',
          type: 'line',
          stack: '总量',
          itemStyle: {
            color: '#00A389',
          },
          data: [12, 13, 10, 34, 40, 20, 21]
        },
        {
          name: '昨日课程学习时长',
          type: 'line',
          stack: '总量',
          itemStyle: {
            color: '#FF5B5B',
          },
          data: [22, 18, 19, 23, 29, 33, 31]
        }
      ]
    };

    myChart.setOption(option);
  }
  const initQuantityAnswersNumber3 = () => {
    const myChart = echarts.init(document.getElementById('quantityAnswersNumberId3'));
    const option = {
      legend: {
        top: 'bottom',
        textStyle: {
          color: '#fff'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)'
      },
      series: [
        {
          name: 'Nightingale Chart',
          type: 'pie',
          radius: [50, 100],
          center: ['50%', '50%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          },
          data: [
            { value: 40, name: '闯关答题量' },
            { value: 38, name: '天梯量' },
            { value: 32, name: '现场答题量' },
            { value: 30, name: '试卷量' },
            { value: 28, name: '课程量' },
            { value: 26, name: '训练量' }
          ]
        }
      ]
    };
    myChart.setOption(option);
  }

</script>

<style lang="less" scoped>
  .teaching-exam-echarts {
    width: 60%;
    margin: 0 auto;
    padding: 1.2rem 0 1rem 0;
    .teaching-exam-btns {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      .teaching-exam-btn-item {
        width: 0.96rem;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
        border-radius: 26px 26px 26px 26px;
        border: 1px solid rgba(255,255,255,0.48);
      }
    }
    .teaching-exam-content {
      display: flex;
      .teaching-exam-content-left {
        flex: 1;
        margin-right: 0.4rem;
        .title {
          font-family: Noto Sans SC;
          font-weight: bold;
          font-size: 0.24rem;
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-bottom: 0.28rem;
        }
        .content {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.28rem;
          .content-item {
            width: 33%;
            padding: 0.28rem;
            background: rgba(17,17,17,0.5);
            border-radius: 0.12rem;
            border: 1px solid rgba(45, 45, 45, 1);
            .content-item-num {
              font-family: Noto Sans SC;
              font-weight: bold;
              font-size: 0.28rem;
              color: #FFFFFF;
              line-height: 28px;
              text-align: center;
              font-style: normal;
              text-transform: none;
              margin-bottom: 0.16rem;
            }
            .content-item-title {
              font-family: Noto Sans SC;
              font-weight: 400;
              font-size: 0.22rem;
              color: #999999;
              line-height: 0.18rem;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
          }
        }
        .quantityAnswersNumber {
          padding: 0.28rem;
          width: 100%;
          height: 358px;
          margin-bottom: 0.26rem;
          background: rgba(17,17,17,0.5);
          border-radius: 0.12rem;
          border: 1px solid rgba(45, 45, 45, 1);
          .title {
            font-family: Noto Sans SC;
            font-weight: bold;
            font-size: 0.24rem;
            color: #FFFFFF;
            line-height: 0.26rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .quantityAnswersNumber-content {
            width: 100%;
            height: 100%;
            margin-top: -45px;
          }
        }
        .certificateHonor {
          padding: 20px;
          width: 100%;
          min-height: 358px;
          margin-bottom: 20px;
          background: rgba(17,17,17,0.5);
          border-radius: 0.12rem;
          border: 1px solid rgba(45, 45, 45, 1);
          .title {
            font-family: Noto Sans SC;
            font-weight: bold;
            font-size: 0.24rem;
            color: #FFFFFF;
            line-height: 23px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .certificateHonor-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .honor-item {
              width: 33%;
              height: 140px;
              border-radius: 0.12rem;
              margin-bottom: 20px;
              .honor-item-img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
  .student-info-wrapper {
    margin-top: 0.65rem;
  }
</style>