<svg width="1920" height="717" viewBox="0 0 1920 717" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="BG">
<g id="Image [triangle-green-hero]" style="mix-blend-mode:overlay" clip-path="url(#clip0_2154_5126)">
<g id="Group" style="mix-blend-mode:overlay" filter="url(#filter0_f_2154_5126)">
<path id="Vector" opacity="0.16" d="M183.874 -214.578L787.388 -261.23L808.106 915.298L183.874 -214.578Z" fill="#A1FFD2"/>
</g>
</g>
<g id="Image [triangle-pink-hero]" style="mix-blend-mode:overlay" clip-path="url(#clip1_2154_5126)">
<g id="Group_2" style="mix-blend-mode:overlay" filter="url(#filter1_f_2154_5126)">
<path id="Vector_2" opacity="0.25" d="M1799.75 -40.163L1319.5 -66.9258L1303.02 608L1799.75 -40.163Z" fill="#FFB9E7"/>
</g>
</g>
<g id="Div [ellipse-4]" style="mix-blend-mode:overlay" opacity="0.23" filter="url(#filter2_f_2154_5126)">
<path d="M610 156C610 -1.95343 738.047 -130 896 -130H1144C1301.95 -130 1430 -1.95343 1430 156C1430 313.953 1301.95 442 1144 442H896C738.047 442 610 313.953 610 156Z" fill="#63E3FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_2154_5126" x="55.8743" y="-389.23" width="880.231" height="1432.53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="64" result="effect1_foregroundBlur_2154_5126"/>
</filter>
<filter id="filter1_f_2154_5126" x="1175.02" y="-194.926" width="752.737" height="930.926" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="64" result="effect1_foregroundBlur_2154_5126"/>
</filter>
<filter id="filter2_f_2154_5126" x="480" y="-260" width="1080" height="832" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="65" result="effect1_foregroundBlur_2154_5126"/>
</filter>
<clipPath id="clip0_2154_5126">
<rect width="992" height="722" fill="white" transform="translate(0 -5)"/>
</clipPath>
<clipPath id="clip1_2154_5126">
<rect width="763" height="608" fill="white" transform="translate(1157)"/>
</clipPath>
</defs>
</svg>
