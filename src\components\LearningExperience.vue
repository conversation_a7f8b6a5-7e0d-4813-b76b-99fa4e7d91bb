
<template>
  <div class="learning-experience">
    <div class="learning-experience__title">学习心得</div>
    <div class="learning-experience__content" v-for="(item, index) in sendInfo" :key="index">
      <div class="user-info__content__item">
        <div class="user-header">
          <img :src="item.userImg" alt="class-img" class="img" />
        </div>
        <div class="user-name-date">
          <div class="user-name">{{item.userName}}</div>
          <div class="user-date">{{item.date}}</div>
        </div>
      </div>
      <div class="user-info__content__text">{{item.value}}</div>
    </div>
    <div class="learning-experience__input">
      <el-input
        v-model="sendValue"
        :autosize="{ minRows: 4, maxRows: 6 }"
        type="textarea"
        placeholder="请输入学习心得"
      />
    </div>
    <div class="learning-experience__submit" @click="sendBtn">发 送</div>
  </div>
</template>

<script setup>
  import class1 from '@/assets/image/class1.png'
  import { ref, onMounted, reactive, watch } from 'vue'
  import defualtIcon from '@/assets/image/defualt.png'
  let sendInfo = reactive([
    
  ])
  const sendValue = ref('')

  let userName = ref('小明同学')
  let userImg = ref(class1)

  onMounted(() => {
    let info = localStorage.getItem('sendInfo') || ''
    if (info) {
      sendInfo.push(...JSON.parse(info))
    } else {
      sendInfo.push({
        value: '学习心得体会篇一 学习是一种永无止境的过程,无论是在学校还是在社会中,都需要我们不断地学习与进步。而对于学习的心得体会,不仅可以让我们更好地理解所学知识,还可以促使我们更加主动地面对，都需要我们不断地学习与进步。',
        userName: '小明同学',
        userImg: class1,
        date: '2024-09-20 10:04:23',
      })
    }

    let studentInfo = localStorage.getItem('userInfo') || ''
    if (studentInfo) {
      userName.value = JSON.parse(studentInfo).userName
      userImg.value = JSON.parse(studentInfo).avatar || defualtIcon
    }
  })

  const sendBtn = () => {
    if(sendValue.value) {
      sendInfo.push({
        value: sendValue.value,
        userName: userName.value,
        userImg: userImg.value || defualtIcon,
        date: new Date().toLocaleString(),
      })
      sendValue.value = ''
      localStorage.setItem('sendInfo', JSON.stringify(sendInfo))
    } else {
      ElMessage({
        message: '请输入发送内容',
        type: 'warning',
      })
    }
  }

</script>

<style scoped lang="less">
  .learning-experience {
    width: 100%;
    min-height: 4.2rem;
    padding: 0.24rem;
    background: rgba(17,17,17,0.5);
    border-radius: 0.12rem;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(110, 110, 110, 1), rgba(45, 45, 45, 1)) 1 1;
    .learning-experience__title {
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.26rem;
      color: #FFFFFF;
      line-height: 23px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 0.3rem;
    }
    .learning-experience__content {
      .user-info__content__item {
        display: flex;
        align-items: center;
        margin-bottom: 0.3rem;
        .user-header {
          width: 0.6rem;
          height: 0.6rem;
          border-radius: 50%;
          border: 2px solid transparent;
          background-image: linear-gradient(to right, rgba(202, 123, 255, 1), rgba(128, 251, 255, 1));
          margin-right: 0.12rem;
          .img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .user-name-date {
          .user-name {
            font-family: Noto Sans SC;
            font-weight: bold;
            font-size: 0.16rem;
            color: #FFFFFF;
            line-height: 0.16rem;
            font-style: normal;
            text-transform: none;
            margin-bottom: 0.12rem;
          }
          .user-date {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.16rem;
            color: #666;
            line-height: 0.16rem;
            font-style: normal;
            text-transform: none;
          }
        }
        
      }
      .user-info__content__text {
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.2rem;
        color: #FFFFFF;
        line-height: 0.3rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 0.3rem;
      }
    }
    .learning-experience__input {
      .el-textarea {
        --el-input-bg-color:rgba(17,17,17,0.5);
        --el-input-hover-border-color:#666;
        --el-input-focus-border-color:#666;
        --el-input-text-color: #FFFFFF;
        --el-input-placeholder-color: #ccc;
        --el-border-color: #363232;
        --el-input-icon-color: #FFFFFF;
      }
    }
    .learning-experience__submit {
      width: 1.2rem;
      height: 0.5rem;
      border-radius: 0.48rem;
      border: 2px solid #FFFFFF;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.18rem;
      color: #FFFFFF;
      line-height: 0.5rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      cursor: pointer;
      margin-top: 0.24rem;
    }
  }
</style>