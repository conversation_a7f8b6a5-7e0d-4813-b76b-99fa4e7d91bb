
<template>
    <div class="cases-wrapper">
      <div class="cases-ys">
        <div class="page-bg2"></div>
        <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">数字艺术</div>
        <div class="case-cy-title wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">爱简艺在数字艺术领域，聚焦于AI城市灯光秀、AI视频制作、AI公共艺术、AI艺术设计策划展览等项目方面，深度探索AI艺术赋能各业态场景的无限可能，为艺术行业、媒体行业及各类关联单位赋能，以人工智能+艺术的垂直形式，积极为行业及城市构建良好的科技艺术应用生态。</div>
        <div class="case-cy">


          <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
            <div class="left-container">
              <div class="title">AI+光影艺术设计</div>
              <div class="desc">利用AI分析艺术，创造动态光影，创新建筑外立面。结合VR、MR、裸眼3D等技术，打破原有的视觉认知，结合当下商业运作机制，创作全新的互动型、科技型的数字视觉体验与空间感受，让艺术更生动，更互动。</div>
            </div>
            <div class="select-container select-container2">
              <el-carousel :interval="5000" direction="vertical" height="5rem">
                <el-carousel-item class="card-item">
                  <div class="items">
                    <video src="https://cdn.aieasyart.com/easyart/video/sztd.mp4" controls="controls" autoplay muted loop class="video"></video>
                  </div>
                  <div slot="name">
                    <div class="title">深中通道艺术光影设计</div>
                    <div class="desc">通过抽象艺术的表达手法，表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。</div>
                  </div>
                </el-carousel-item>
                <el-carousel-item class="card-item">
                  <div class="items">
                    <video src="https://cdn.aieasyart.com/easyart/video/dengguang.mp4" controls="controls" autoplay muted loop class="video"></video>
                  </div>
                  <div slot="name">
                    <div class="title">深圳福田中心城区艺术光影设计</div>
                    <div class="desc">以创新与卓越为核心，融合了现代科技与艺术手法，展现出深圳的独特魅力。</div>
                  </div>
                </el-carousel-item>
                <el-carousel-item class="card-item">
                  <div class="items">
                    <video src="https://cdn.aieasyart.com/easyart/video/futian.mp4" controls="controls" autoplay muted loop class="video"></video>
                  </div>
                  <div slot="name">
                    <div class="title">深圳福田平安金融中心光影艺术</div>
                    <div class="desc">"深蓝交响"主题灯光秀，通过光影艺术，让观众感受到深圳海洋的生机与活力。</div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>


          <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
            <div class="select-container">
              <el-carousel :interval="5000" height="5.5rem">
                <el-carousel-item class="card-item">
                  <div class="items">
                    <img :src="ys12" alt="" class="img2">
                    <img :src="ys13" alt="" class="img2">
                  </div>
                  <h3 slot="name" class="title">
                    <div class="title">中山大学（深圳校区）景观设计</div>
                    <div class="desc">通过人工智能+艺术设计，将中国榫卯建筑形式创新运用于现代设计中，彰显了传统工艺的美学价值及与科技融合的贴合性。</div>
                  </h3>
                </el-carousel-item>
                <el-carousel-item class="card-item">
                  <div class="items">
                    <img :src="ys14" alt="" class="img2">
                    <img :src="ys15" alt="" class="img2">
                  </div>
                  <h3 slot="name" class="title">
                    <div class="title">中山大学（深圳校区）学人苑酒店设计</div>
                    <div class="desc">中山大学深圳校区学人苑酒店的软装设计，融合了校园文化与学术精神，通过视觉艺术的形式，精心展现了中山大学深厚的历史底蕴和学科特色。</div>
                  </h3>
                </el-carousel-item>
                <el-carousel-item class="card-item">
                  <div class="items">
                    <img :src="ys16" alt="" class="img2">
                    <img :src="ys17" alt="" class="img2">
                  </div>
                  <h3 slot="name" class="title">
                    <div class="title">广东外语外贸大学校友墙设计</div>
                    <div class="desc">通过人工智能+艺术设计工具，用科技赋能建筑外立面的设计方案，为广外提供新颖多样的校友捐赠墙设计理念。</div>
                  </h3>
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="left-container">
              <div class="title">AI+场景艺术设计</div>
              <div class="desc">通过深度学习算法和大数据分析，实现个性化和智能化的场景体验。我们利用先进的AI技术与创意设计相结合，为企业提供定制化的智能场景解决方案，从用户行为分析到场景互动优化，全方位提升用户体验，实现品牌价值的最大化。</div>
            </div>
          </div>


          <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
            <div class="left-container">
              <div class="title">AI+视频制作</div>
              <div class="desc">AI与创新视觉的融合，在新媒体时代下创造更多“新媒体内容”。利用先进的AI技术与视频制作创意呈现高级恢弘的视频效果，用新颖创意与富有创造性的画面，制作生成眼前一新的AI视频。</div>
            </div>
            <div class="right-container type1">
              <video src="https://cdn.aieasyart.com/easyart/video/ai-shipin.mp4" controls="controls" autoplay muted loop  class="img"></video>
            </div>
          </div>

        </div>
      </div>
    </div>
    
  </template>
  
  <script setup>
  
    import ys1 from '@/assets/case/yishu/ys1.jpg'
    import ys2 from '@/assets/case/yishu/ys2.jpg'
    import ys3 from '@/assets/case/yishu/ys3.jpg'
    import ys4 from '@/assets/case/yishu/ys4.jpg'
    import ys5 from '@/assets/case/yishu/ys5.jpg'
    import ys6 from '@/assets/case/yishu/ys6.jpg'
    import ys12 from '@/assets/case/yishu/ys12.jpg'
    import ys13 from '@/assets/case/yishu/ys13.jpg'
    import ys14 from '@/assets/case/yishu/ys14.jpg'
    import ys15 from '@/assets/case/yishu/ys15.jpg'
    import ys16 from '@/assets/case/yishu/ys16.jpg'
    import ys17 from '@/assets/case/yishu/ys17.jpg'
  
    const caseList = [
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '深圳平安金融中心光影艺术',
        text: '深圳以其丰富的海洋资源和现代科技，成为人与自然和谐共生的象征。"深蓝交响"主题灯光秀，通过光影艺术，让观众感受到深圳海洋的生机与活力。珊瑚、鱼群、红树林，这些自然元素构成了海洋生态的多样性，而深圳人的传统生活方式与现代科技的融合，更是展现了人与海洋的紧密联系。',
        showType: 'line',
        images: [
          {id: 0, name: ys5},
          {id: 1, name: ys6}
        ]
      },
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '中山大学（深圳校区）景观设计',
        text: '通过人工智能+艺术设计，将中国榫卯建筑形式创新运用于现代设计中，彰显了传统工艺的美学价值及与科技融合的贴合性',
        showType: 'line',
        images: [
          {id: 0, name: ys12},
          {id: 1, name: ys13}
        ]
      },
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '福田中心城区建筑外立面光影设计',
        text: '以创新与卓越为核心，融合了现代科技与艺术手法，展现出城市的独特魅力。这座建筑不仅追求设计的卓越，更巧妙地将中国传统美学与现代设计相结合，尤其是汲取了宋代美学的精髓，通过灯光艺术的形式，彰显深圳多元文化和国际化视野。',
        showType: 'line',
        images: [
          {id: 0, name: ys3},
          {id: 1, name: ys4}
        ]
      },
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '科技、艺术、AI人工智能——深中通道艺术光影设计',
        text: '动画采用科技、艺术、AI人工智能三种手法呈现。通过抽象艺术的表达手法,表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。各主题用极具科技感的现代画面展现深圳年轻与创造力。',
        showType: 'line',
        images: [
          {id: 0, name: ys1},
          {id: 1, name: ys2}
        ]
      },
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '中山大学（深圳校区）学人苑酒店设计',
        text: '',
        showType: 'line',
        images: [
          {id: 0, name: ys14},
          {id: 1, name: ys15}
        ]
      },
      {
        key: 'ys',
        title: '数字艺术版块',
        subTitle: '广东外语外贸大学校友墙设计',
        text: '通过人工智能+艺术设计工具，用科技赋能建筑外立面的设计方案，为广外提供新颖多样的校友捐赠墙设计理念。',
        showType: 'line',
        images: [
          {id: 0, name: ys16},
          {id: 1, name: ys17}
        ]
      }
    ]
  
  </script>
  
  <style lang="less" scoped>
    .cases-wrapper {
      width: 100%;
      margin: 0 auto;
      padding: 1rem 0 0 0;
      .cases-ys {
        width: 100%;
        position: relative;
        padding-bottom: 0.3rem;
        .page-bg2 {
          background-image: url('@/assets/bg/bg7.jpg');
          background-size: cover;
          background-repeat: no-repeat;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          opacity: 0.4;
          z-index: -1;
        }
        .big-title {
          width: 60%;
          margin: 0 auto;
          padding-top: 60px;
          margin-bottom: 0.5rem;
          font-family: Noto Sans SC;
          font-weight: bold;
          font-size: 0.5rem;
          color: #FFFFFF;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .case-cy-title {
          width: 60%;
          margin: 0 auto;
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 0.26rem;
          color: rgba(255, 255, 255, 1);
          text-align: left;
          letter-spacing: 0.02rem;
          margin-bottom: 1rem;
        }
        .case-cy {
          width: 60%;
          margin: 0 auto;
          .case-cy-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            .left-container {
              width: 40%;
              .title {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.32rem;
                color: #71deb6;
                text-align: left;
                margin-bottom: 0.2rem
              }
              .desc {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.24rem;
                color: rgba(255, 255, 255, 1);
                text-align: left;
              }
            }
            .right-container {
              width: 53%;
              padding: 0.3rem;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 0.1rem;
              background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
              .img {
                width: 100%;
                object-fit: cover;
                border-radius: 0.1rem;
              }
            }
            .select-container {
              width: 57%;
              padding: 0.3rem;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 0.1rem;
              background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
              /deep/ .el-carousel {
                width: 100%;
                .el-carousel__container {
                  .items {
                    display: flex;
                    .img {
                      width: 95%;
                      height: 3.6rem;
                      border-radius: 0.1rem;
                      &:last-child {
                        margin-right: 0;
                      }
                      object-fit: cover;
                    }
                    .img1 {
                      height: 3.6rem;
                      text-align: center;
                      border-radius: 0.1rem;
                    }
                    .video {
                      width: 95%;
                      height: 3.6rem;
                      border-radius: 0.1rem;
                      background: #000;
                    }
                    .img2 {
                      width: 49%;
                      height: 3.75rem;
                      margin-right: 0.2rem;
                      object-fit: cover;
                      border-radius: 0.1rem;
                      &:last-child {
                        margin-right: 0;
                      }
                    }
                    .img3 {
                      width: 100%;
                      height: 5rem;
                      object-fit: cover;
                      border-radius: 0.1rem;
                    }
                  }
                  .center {
                    align-items: center;
                    justify-content: center;
                  }
                  .title {
                    font-family: Noto Sans SC;
                    font-weight: 600;
                    font-size: 0.3rem;
                    color: #000;
                    text-align: left;
                    margin-top: 0.2rem;
                  }
                  .desc {
                    font-family: Noto Sans SC;
                    font-weight: 400;
                    font-size: 0.22rem;
                    color: #000;
                    text-align: left;
                    width: 93%;
                    margin-top: 0.08rem;
                  }
                }
                .el-carousel__indicators {
                  bottom: -0.18rem;
                  .el-carousel__indicator {
                    .el-carousel__button {
                      height: 0.03rem;
                      width: 0.4rem;
                      background-color: #000;
                      border-radius: 0.1rem;
                    }
                  }
                  .el-carousel__indicator.is-active button {
                    background: linear-gradient(90deg, #fc6756 3.87%, #f8cf3e 39.76%, #26f4d0 52.98%, #724ce8 98.32%);
                  }
                }
              }
            }
            .select-container2 {
              /deep/ .el-carousel {
                .el-carousel__indicators {
                  right: -0.12rem;
                  .el-carousel__indicator {
                    .el-carousel__button {
                      height: 0.4rem;
                      width: 0.03rem;
                      background-color: #000;
                      border-radius: 0.1rem;
                    }
                  }
                  .el-carousel__indicator.is-active button {
                    background: linear-gradient(180deg, #fc6756 3.87%, #f8cf3e 39.76%, #26f4d0 52.98%, #724ce8 98.32%);
                  }
                }
              }
            }
          }
        }
      }
    }
  </style>