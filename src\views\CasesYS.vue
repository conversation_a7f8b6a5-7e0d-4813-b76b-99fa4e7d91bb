<template>
  <div class="cases-wrapper">
    <div class="cases-ys">
      <div class="page-bg2"></div>
      <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">
        数字艺术
      </div>
      <div class="case-cy-title wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
        爱简艺在数字艺术领域，聚焦于AI城市灯光秀、AI视频制作、AI公共艺术、AI艺术设计策划展览等项目方面，深度探索AI艺术赋能各业态场景的无限可能，为艺术行业、媒体行业及各类关联单位赋能，以人工智能+艺术的垂直形式，积极为行业及城市构建良好的科技艺术应用生态。
      </div>
      <div class="case-cy">
        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI+光影艺术设计</div>
            <div class="desc">
              利用AI分析艺术，创造动态光影，创新建筑外立面。结合VR、MR、裸眼3D等技术，打破原有的视觉认知，结合当下商业运作机制，创作全新的互动型、科技型的数字视觉体验与空间感受，让艺术更生动，更互动。
            </div>
          </div>
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/sztd.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <div slot="name">
                  <div class="title">深中通道艺术光影设计</div>
                  <div class="desc">
                    通过抽象艺术的表达手法，表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/dengguang.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <div slot="name">
                  <div class="title">深圳福田中心城区艺术光影设计</div>
                  <div class="desc">
                    以创新与卓越为核心，融合了现代科技与艺术手法，展现出深圳的独特魅力。
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video
                    src="https://cdn.aieasyart.com/easyart/video/futian.mp4"
                    controls="controls"
                    autoplay
                    muted
                    loop
                    class="video"
                  ></video>
                </div>
                <div slot="name">
                  <div class="title">深圳福田平安金融中心光影艺术</div>
                  <div class="desc">
                    "深蓝交响"主题灯光秀，通过光影艺术，让观众感受到深圳海洋的生机与活力。
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container">
            <el-carousel :interval="5000" height="5.5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="ys12" alt="" class="img2" />
                  <img :src="ys13" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">中山大学（深圳校区）景观设计</div>
                  <div class="desc">
                    通过人工智能+艺术设计，将中国榫卯建筑形式创新运用于现代设计中，彰显了传统工艺的美学价值及与科技融合的贴合性。
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="ys14" alt="" class="img2" />
                  <img :src="ys15" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">中山大学（深圳校区）学人苑酒店设计</div>
                  <div class="desc">
                    中山大学深圳校区学人苑酒店的软装设计，融合了校园文化与学术精神，通过视觉艺术的形式，精心展现了中山大学深厚的历史底蕴和学科特色。
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img :src="ys16" alt="" class="img2" />
                  <img :src="ys17" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">广东外语外贸大学校友墙设计</div>
                  <div class="desc">
                    通过人工智能+艺术设计工具，用科技赋能建筑外立面的设计方案，为广外提供新颖多样的校友捐赠墙设计理念。
                  </div>
                </h3>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div class="left-container">
            <div class="title">AI+场景艺术设计</div>
            <div class="desc">
              通过深度学习算法和大数据分析，实现个性化和智能化的场景体验。我们利用先进的AI技术与创意设计相结合，为企业提供定制化的智能场景解决方案，从用户行为分析到场景互动优化，全方位提升用户体验，实现品牌价值的最大化。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">AI+视频制作</div>
            <div class="desc">
              AI与创新视觉的融合，在新媒体时代下创造更多“新媒体内容”。利用先进的AI技术与视频制作创意呈现高级恢弘的视频效果，用新颖创意与富有创造性的画面，制作生成眼前一新的AI视频。
            </div>
          </div>
          <div class="right-container type1">
            <video
              src="https://cdn.aieasyart.com/easyart/video/ai-shipin.mp4"
              controls="controls"
              autoplay
              muted
              loop
              class="img"
            ></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc">
                    <!-- 通过抽象艺术的表达手法，表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。 -->
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品镜头</div>
                  <div class="desc">
                    <!-- 以创新与卓越为核心，融合了现代科技与艺术手法，展现出深圳的独特魅力。 -->
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <!-- <video src="" controls="controls" autoplay muted loop class="video"></video> -->
                  <img :src="ys17" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">产品关键帧图片</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <!-- <video src="" controls="controls" autoplay muted loop class="video"></video> -->
                  <img :src="ys17" alt="" class="img2" />
                </div>
                <div slot="name">
                  <div class="title">产品关键帧图片</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>

          <div class="left-container">
            <div class="title">新媒体平台批量视频-伊利奶粉产品</div>
            <div class="desc">
              根据伊利提供的产品图，进行产品图像生成模型训练，达到快速生成批量、多角度产品场景图作为视频关键帧图片，结合自建工作流的批量视频脚本制作、批量视频生成及混剪模型，满足品牌方日产200条5s短视频的需求。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">新媒体平台批量视频-医药产品</div>
            <div class="desc">
              根据医药品牌的主推产品，输出符合产品的30s短视频及大量5s-10s产品延展镜头，提高品牌方视频内容需求获取效率，降低组织成本，取得更多视频风格、人物、场景差异化内容，为品牌官方号、矩阵号投放创造更多可能。
            </div>
          </div>
          <div class="select-container select-container2">
            <el-carousel :interval="5000" direction="vertical" height="5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">30秒广告去敏版</div>
                  <div class="desc">
                    <!-- 通过抽象艺术的表达手法，表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。 -->
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc">
                    <!-- 以创新与卓越为核心，融合了现代科技与艺术手法，展现出深圳的独特魅力。 -->
                  </div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                  <!-- <img :src="ys17" alt="" class="img2" /> -->
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <video src="" controls="controls" autoplay muted loop class="video"></video>
                  <!-- <img :src="ys17" alt="" class="img2" /> -->
                </div>
                <div slot="name">
                  <div class="title">产品延展镜头</div>
                  <div class="desc"></div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">父亲的回忆录（2025父亲节）</div>
            <div class="desc">
              生于上世纪60年代初小乡村的他，努力考上了大学，在大学遇到了心仪的女孩，毕业后到了国企工厂做白领,跟女孩结婚生子，后遭遇下岗潮，下海创业，努力奋斗了一辈子，后来他的孩子也渐渐长大，也当上了爸爸。他是那个年代千千万万人的缩影，是一位普通却不平凡的父亲。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">广州移动“数据要素x AI”生态宣传片</div>
            <div class="desc">
              爱简艺作为广州移动在AIGC领域的核心生态伙伴，积极协作构建AI+等端到端解决方案，为AI+时代注入澎湃新动能，实现AI规模发展、规模应用，加快形成AI技术能力、经济效益上的规模效应，全面赋能数字经济发展、数字政府服务、数字文化打造、数字社会构建和数字生态文明建设的新进程。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">深圳南山区住建局IP宣传视频</div>
            <div class="desc">
              爱简艺通过全流程AI进行南山区住建局燃气安全主管部门宣传IP的设计，并制作宣传视频，制作时长仅为传统方式的十分之一，由南山住建局发布至南山住建视频号。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">品牌产品形象宣传片（产品宣传视频）</div>
            <div class="desc">AI新演绎丨智能新境界</div>
            <div class="desc">
              爱简艺作为广州移动在AIGC领域的核心生态伙伴，积极协作构建AI+等端到端解决方案，为AI+时代注入澎湃新动能，实现AI规模发展、规模应用，加快形成AI技术能力、经济效益上的规模效应，全面赋能数字经济发展、数字政府服务、数字文化打造、数字社会构建和数字生态文明建设的新进程。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">IP形象宣传片（IP+节日）</div>
            <div class="desc">AI新演绎丨IP形象新空间</div>
            <div class="desc">
              爱简艺IP形象宣传片，AI与传统IP形象延展应用的创新融合，新的故事、新的互动、新的印象，灵活呈现形象特点，种草每一处细节，引领品牌形象的全新呈现。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">《北京欢迎你》宣传视频（文旅/IP）</div>
            <div class="desc">AI新演绎丨IP形象新空间</div>
            <div class="desc">
              爱简艺
              IP形象宣传片，AI与传统IP形象延展应用的创新融合，新的故事、新的互动、新的印象，灵活呈现形象特点，种草每一处细节，引领品牌形象的全新呈现。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">《TOPSKYLINE》2025湾区时尚力量天空大秀</div>
            <div class="desc">福美x爱简艺，深圳CBD化身城市秀场，AI灯光秀点亮湾区可持续之夜！</div>
            <div class="desc">
              这次灯光秀与秀场活动的联动，展现了现代品牌传播的新思路——通过打造具有艺术价值的城市事件，让品牌信息以最自然的方式触达受众。当灯光秀本身成为人们主动关注的热点，品牌传播就实现了从"要人看"到"人要看"的转变。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">《灵蛇启航·再创辉煌》深圳市民中心春节特别版（2025）灯光秀</div>
            <!-- <div class="desc">AI新演绎丨IP形象新空间</div> -->
            <div class="desc">
              本次内容以【体育华章】、【财富双至】与【蛇年大吉】为代表的多个章节光影秀，更好展现深圳的创新精神与传承中国传统文化的坚守，并体现深圳打造体育名城的决心，同时也为观众带来新年的美好祝愿，共同欢度充满希望和机遇的2025新年。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">广东省文旅厅-请到广东过大年（2025）启动仪式光影艺术</div>
            <div class="desc">
              2025年1月8日，由广东省文化和旅游厅、中央广播电视总台广东总站及深圳市人民政府主办，深圳市文化广电旅游体育局、深圳市福田区人民政府承办的“请到广东过大年”2025年广东文化和旅游消费促进活动启动仪式在深圳正式举行。
            </div>
            <div class="desc">
              爱简艺光影艺术团队以深圳市民中心的43栋建筑楼体为幕，与深圳大漠大智控技术有限公司无人机团队共同合作，为启动仪式上演了一场主题为“请到广东过大年”与“辉煌新时代”的主题灯光秀表演。“烟花”、“灯笼”、“福”、“祥云”、“中国结”等具有寓意祝福的春节元素，通过影音交互的方式传递了“请到广东过大年”活动主旨，奏响2025新春祝福。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">中国人寿主题《光影交响 璀璨同行》光影艺术（2025）</div>
            <!-- <div class="desc">AI新演绎丨IP形象新空间</div> -->
            <div class="desc">
              本次爱简艺为中国人寿定制的的大型主题灯光秀，以企业核心价值出发，中国人寿历经七十六年风雨征程，以“诺”为笔，以“担当”为墨，服务国家发展大局，相伴人民美好生活，书写了一部部波澜壮阔、气势磅礴的企业史诗，爱简艺以AI光影艺术设计，上映中国人寿“光影交响
              璀璨同行”主题灯光秀，点亮深圳市民中心的夜空。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">荣耀400 x 爱简艺——商业主题广告灯光秀</div>
            <div class="desc">
              建筑在呼吸，光影在叙事，而我们看到的，不止是一场灯光秀，更是一次关于科技、艺术与营销的深度对话，
            </div>
            <div class="desc">
              爱简艺以AI主导创作“荣耀400”与代言人肖战的专题灯光秀，体现了AI与品牌、商业营销与科技的更密切融合，AI不再是冰冷的展示工具，更是承载情感与叙事的载体。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">中国海洋经济博览会暨深圳国际海洋周（2024）光影艺术</div>
            <div class="desc">
              爱简艺以“深蓝交响：深圳海洋的和谐乐章”主题引领，通过人工智能技术创新艺术设计的表现形式，结合自研光影艺术设计垂直模型，为福田区市民广场43栋建筑的光影展现进行内容策划与制作。
            </div>
            <div class="desc">
              光影秀的总体设计概念分为：《生命之源：海洋的奥秘与丰饶》、《海洋活力：现代海洋产业与科技》和《海洋城市：深圳的海洋梦想》三个主题，以科技与艺术向观众展现深圳海洋生态的景色和文化，也进一步强调了“经济发展与海洋生态保护要平衡发展”的话题，契合本届海博会的举办宗旨，更是呈现出科技、艺术与海洋经济文化内涵的完美共融，奉上精彩的视觉盛宴。
            </div>
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
            <div class="desc"></div>
            
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">深中通道深圳门户片区（2024）光影艺术</div>
            <div class="desc">
              光影艺术设计以其独特的视觉叙事，展现了深圳与中山文化的交融，也彰显了深中通道作为粤港澳大湾区内互联互通的纽带和区域一体化发展的典范，强调了协同发展和创新引领的重要性。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">广州国际灯光秀《多彩湾区》光影艺术（2024）</div>
            <div class="desc">
              爱简艺本次展现的「多彩湾区」作品，将珠三角地区的11个城市各自代表性花卉和地标性建筑以粒子形式并配合具象建筑视频变化进行呈现，利用艺术表达，充分运用粤港澳的代表文化元素，力求用文化讲好时代故事。通过将AIGC与视觉艺术融合的方式，让到场观众能够提前感受人工智能发展下的多彩生活，体验多彩粤港澳的独特魅力。
            </div>
            <!-- <div class="desc">aaa</div> -->
          </div>
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="right-container type1">
            <video src="" controls="controls" autoplay muted loop class="img"></video>
          </div>
          <div class="left-container">
            <div class="title">肇庆大湾镇百千万工程河道艺术亮化</div>
            <div class="desc">
              以“美学思维推进典型镇”的建设理念，爱简艺在此次大湾镇的夜景照明设计方案中，通过融合“AI+光影艺术”的设计手法对大湾公园、文化广场、长堤步道、鱼跃龙门公园四个点位策划设计灯光光影元素，将大湾镇特有的“一木两鱼”文化特色融入到夜晚的照明景观之中，让镇街的夜晚焕发出独特的魅力，展现出大湾镇与科技的深度融合，成功将这座历史悠久的小镇装点得如梦如幻。
            </div>
          </div>
        </div>

        <div class="case-cy-item wow animate__animated animate__fadeInUp" data-wow-duration="2s">
          <div class="left-container">
            <div class="title">动画IP制作案例</div>
            <div class="desc">爱简艺原创IP-米多多《长生烬》</div>
          </div>
          <div class="select-container">
            <el-carousel :interval="5000" height="5.5rem">
              <el-carousel-item class="card-item">
                <div class="items">
                  <img src="" alt="" class="img2" />
                  <img src="" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人物设定</div>
                  <div class="desc">
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img src="" alt="" class="img2" />
                  <img src="" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人物设定</div>
                  <div class="desc">
                  </div>
                </h3>
              </el-carousel-item>
              <el-carousel-item class="card-item">
                <div class="items">
                  <img src="" alt="" class="img2" />
                  <img src="" alt="" class="img2" />
                </div>
                <h3 slot="name" class="title">
                  <div class="title">人物设定</div>
                  <div class="desc">
                  </div>
                </h3>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ys1 from '@/assets/case/yishu/ys1.jpg'
import ys2 from '@/assets/case/yishu/ys2.jpg'
import ys3 from '@/assets/case/yishu/ys3.jpg'
import ys4 from '@/assets/case/yishu/ys4.jpg'
import ys5 from '@/assets/case/yishu/ys5.jpg'
import ys6 from '@/assets/case/yishu/ys6.jpg'
import ys12 from '@/assets/case/yishu/ys12.jpg'
import ys13 from '@/assets/case/yishu/ys13.jpg'
import ys14 from '@/assets/case/yishu/ys14.jpg'
import ys15 from '@/assets/case/yishu/ys15.jpg'
import ys16 from '@/assets/case/yishu/ys16.jpg'
import ys17 from '@/assets/case/yishu/ys17.jpg'

const caseList = [
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '深圳平安金融中心光影艺术',
    text: '深圳以其丰富的海洋资源和现代科技，成为人与自然和谐共生的象征。"深蓝交响"主题灯光秀，通过光影艺术，让观众感受到深圳海洋的生机与活力。珊瑚、鱼群、红树林，这些自然元素构成了海洋生态的多样性，而深圳人的传统生活方式与现代科技的融合，更是展现了人与海洋的紧密联系。',
    showType: 'line',
    images: [
      { id: 0, name: ys5 },
      { id: 1, name: ys6 }
    ]
  },
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '中山大学（深圳校区）景观设计',
    text: '通过人工智能+艺术设计，将中国榫卯建筑形式创新运用于现代设计中，彰显了传统工艺的美学价值及与科技融合的贴合性',
    showType: 'line',
    images: [
      { id: 0, name: ys12 },
      { id: 1, name: ys13 }
    ]
  },
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '福田中心城区建筑外立面光影设计',
    text: '以创新与卓越为核心，融合了现代科技与艺术手法，展现出城市的独特魅力。这座建筑不仅追求设计的卓越，更巧妙地将中国传统美学与现代设计相结合，尤其是汲取了宋代美学的精髓，通过灯光艺术的形式，彰显深圳多元文化和国际化视野。',
    showType: 'line',
    images: [
      { id: 0, name: ys3 },
      { id: 1, name: ys4 }
    ]
  },
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '科技、艺术、AI人工智能——深中通道艺术光影设计',
    text: '动画采用科技、艺术、AI人工智能三种手法呈现。通过抽象艺术的表达手法,表现深中通道作为粤港澳大湾区高流量、高感知的超级门户。各主题用极具科技感的现代画面展现深圳年轻与创造力。',
    showType: 'line',
    images: [
      { id: 0, name: ys1 },
      { id: 1, name: ys2 }
    ]
  },
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '中山大学（深圳校区）学人苑酒店设计',
    text: '',
    showType: 'line',
    images: [
      { id: 0, name: ys14 },
      { id: 1, name: ys15 }
    ]
  },
  {
    key: 'ys',
    title: '数字艺术版块',
    subTitle: '广东外语外贸大学校友墙设计',
    text: '通过人工智能+艺术设计工具，用科技赋能建筑外立面的设计方案，为广外提供新颖多样的校友捐赠墙设计理念。',
    showType: 'line',
    images: [
      { id: 0, name: ys16 },
      { id: 1, name: ys17 }
    ]
  }
]
</script>

<style lang="less" scoped>
.cases-wrapper {
  width: 100%;
  margin: 0 auto;
  padding: 1rem 0 0 0;
  .cases-ys {
    width: 100%;
    position: relative;
    padding-bottom: 0.3rem;
    .page-bg2 {
      background-image: url('@/assets/bg/bg7.jpg');
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.4;
      z-index: -1;
    }
    .big-title {
      width: 60%;
      margin: 0 auto;
      padding-top: 60px;
      margin-bottom: 0.5rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .case-cy-title {
      width: 60%;
      margin: 0 auto;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.26rem;
      color: rgba(255, 255, 255, 1);
      text-align: left;
      letter-spacing: 0.02rem;
      margin-bottom: 1rem;
    }
    .case-cy {
      width: 60%;
      margin: 0 auto;
      .case-cy-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        .left-container {
          width: 40%;
          .title {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.32rem;
            color: #71deb6;
            text-align: left;
            margin-bottom: 0.2rem;
          }
          .desc {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.24rem;
            color: rgba(255, 255, 255, 1);
            text-align: left;
          }
        }
        .right-container {
          width: 53%;
          padding: 0.3rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.1rem;
          background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
          .img {
            width: 100%;
            object-fit: cover;
            border-radius: 0.1rem;
          }
        }
        .select-container {
          width: 57%;
          padding: 0.3rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.1rem;
          background: linear-gradient(230.94deg, rgb(229, 251, 251) 8.02%, rgb(255, 252, 243) 100%);
          /deep/ .el-carousel {
            width: 100%;
            .el-carousel__container {
              .items {
                display: flex;
                .img {
                  width: 95%;
                  height: 3.6rem;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                  object-fit: cover;
                }
                .img1 {
                  height: 3.6rem;
                  text-align: center;
                  border-radius: 0.1rem;
                }
                .video {
                  width: 95%;
                  height: 3.6rem;
                  border-radius: 0.1rem;
                  background: #000;
                }
                .img2 {
                  width: 49%;
                  height: 3.75rem;
                  margin-right: 0.2rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                  &:last-child {
                    margin-right: 0;
                  }
                }
                .img3 {
                  width: 100%;
                  height: 5rem;
                  object-fit: cover;
                  border-radius: 0.1rem;
                }
              }
              .center {
                align-items: center;
                justify-content: center;
              }
              .title {
                font-family: Noto Sans SC;
                font-weight: 600;
                font-size: 0.3rem;
                color: #000;
                text-align: left;
                margin-top: 0.2rem;
              }
              .desc {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.22rem;
                color: #000;
                text-align: left;
                width: 93%;
                margin-top: 0.08rem;
              }
            }
            .el-carousel__indicators {
              bottom: -0.18rem;
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.03rem;
                  width: 0.4rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  90deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
        .select-container2 {
          /deep/ .el-carousel {
            .el-carousel__indicators {
              right: -0.12rem;
              .el-carousel__indicator {
                .el-carousel__button {
                  height: 0.4rem;
                  width: 0.03rem;
                  background-color: #000;
                  border-radius: 0.1rem;
                }
              }
              .el-carousel__indicator.is-active button {
                background: linear-gradient(
                  180deg,
                  #fc6756 3.87%,
                  #f8cf3e 39.76%,
                  #26f4d0 52.98%,
                  #724ce8 98.32%
                );
              }
            }
          }
        }
      }
    }
  }
}
</style>
