{"name": "easy-art-home", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "animate.css": "^4.1.1", "axios": "^1.7.7", "echarts": "^5.5.1", "element-plus": "^2.8.3", "lodash": "^4.17.21", "pinia": "^2.1.7", "swiper": "^11.1.14", "typeit": "^8.8.5", "vue": "^3.4.29", "vue-router": "^4.3.3", "vue3-lazy": "^1.0.0-alpha.1", "wow.js": "^1.2.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.1.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "prettier": "^3.2.5", "typescript": "~5.4.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.19", "vite-plugin-vue-devtools": "^7.4.5", "vue-tsc": "^2.0.21"}}