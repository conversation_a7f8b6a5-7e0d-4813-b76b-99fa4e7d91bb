

<template>
  <div class="teaching-exam-detail">
    <div class="teaching-exam-detail__content">
      <div class="content-container">
        <div class="video-container">
          <div ref="title" class="title"></div>
          <div ref="desc"></div>
        </div>
      </div>
      <Handout :actives="activeNames"/>
      <div class="marginBottom20"></div>
      <LearningExperience />
    </div>
    <StudentInfo />
  </div>
</template>

<script setup>
  import TypeIt from 'typeit'
  import { ref, computed, onMounted } from 'vue';
  import {useRoute} from 'vue-router';

  import StudentInfo from '@/components/StudentInfo.vue';
  import Handout from '@/components/Handout.vue';
  import LearningExperience from '@/components/LearningExperience.vue';

  const activeNames = ref([1,2,3]);
  let route = useRoute()
  let remark = ref('');
  let titleName = ref('');
  const desc = ref(null)
  const title = ref(null)

  onMounted(() => {
    let token = localStorage.getItem('token');
    if (!token) {
      window.location.href = '/teachingExam'
    }
    let pageIndex = route.query.index
    let data = JSON.parse(localStorage.getItem('studentInfo'))
    data.forEach((item) => {
      if( pageIndex == item.id) {
        titleName.value = item.name;  //标题
        remark.value = item.remark;
      }
    });
    
    new (TypeIt)(title.value, {
        strings: [titleName.value],
        speed: 50,
        html: true,//将字符串视为HTML
        afterComplete: (e) => {
          e.destroy();
          new (TypeIt)(desc.value, {
            strings: [remark.value],
            speed: 45,
            html: true,//将字符串视为HTML
            /* afterComplete: (e) => {
              e.destroy();
            } */
          }).go()
        }
    }).go()
    
  })

</script>

<style lang="less" scoped>
  .teaching-exam-detail {
    width: 60%;
    margin: 0 auto;
    padding: 1.2rem 0 1rem 0;
    display: flex;
    .teaching-exam-detail__content {
      flex: 1;
      margin-right: 0.4rem;
      .content-container {
        padding: 0.26rem;
        margin-bottom: 0.26rem;
        min-height: 3rem;
        background: rgba(17,17,17,0.5);
        border-radius: 0.12rem;
        border: 1px solid rgba(110, 110, 110, 1);
        border-image: linear-gradient(180deg, rgba(110, 110, 110, 1), rgba(45, 45, 45, 1)) 1 1;
        .video-container {
          min-height: 2.7rem;
          background: #000;
          font-size: 0.22rem;
          padding: 0.2rem 0.3rem;
          border-radius: 0.12rem;
          letter-spacing: 0.03rem;
          video {
            border-radius: 0.12rem;
          }
          .title {
            font-size: 0.26rem;
            margin-bottom: 0.16rem;
          }
        }
        .video-name {
          font-family: Noto Sans SC;
          font-weight: bold;
          font-size: 0.28rem;
          color: #FFFFFF;
          font-style: normal;
          text-transform: none;
          margin-top: 0.24rem;
          margin-bottom: 0.22rem;
        }
        .video-num {
          font-family: Noto Sans SC;
          font-weight: bold;
          font-size: 0.2rem;
          color: #FFFFFF;
          line-height: 16px;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
</style>