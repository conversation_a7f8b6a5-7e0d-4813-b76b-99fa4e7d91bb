<script setup>
  import { RouterView, useRouter, useRoute } from 'vue-router'
  import Header from '@/components/Header.vue'
  import Footer from '@/components/Footer.vue'
  import TopUp from '@/components/TopUp.vue'
  import footerImage from '@/assets/svg/footer.svg'
  let route = useRoute()
</script>

<template>
  <div class="easy-art-layout">
    <Header />
    <div class="easy-art-content">
      <RouterView />
    </div>
    <!-- <div class="line-bottom"></div> -->
    <div class="background-circle" v-if="route.name !== 'home' && route.name !== 'digitalArt' && route.name !== 'digitalIndustry' && route.name !== 'digitalEducation'">
     <!--  <div class="line-left-short"></div>
      <div class="line-left"></div>
      <div class="line-right-short"></div>
      <div class="line-right"></div> -->
    </div>
    <div class="bottom-circle">
      <img v-lazy="footerImage" alt="" class="bottom-circle-img" />
    </div>
    <Footer />
    <TopUp />
  </div>
</template>

<style lang="less" src="@/assets/index.less" />