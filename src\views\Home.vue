
<script setup>
  import axios from 'axios';
  import { ref, onMounted, reactive } from 'vue'
  import WOW from "wow.js";
  // import InfiniteScroll from '@/components/InfiniteScroll.vue'
  import MuteIcon from '@/assets/image/Mute.png'
  import PlayIcon from '@/assets/image/Play.png'
  import Upload from '@/components/Upload.vue'
  import router from '@/router';
  import CollaborationConsultation from './CollaborationConsultation.vue'
/*   import szcy from '@/assets/image/szcy.jpg'
  import szjy from '@/assets/image/szjy.jpg'
  import szys from '@/assets/image/szys.jpg' */
  
  const uploadParentRef = ref(null)
  const inputValue = ref('')
  let iamgeInfo = reactive([])
  let percentage = ref(10)
  let isLoading = ref(false)
  let isImgToImg = ref(false)
  let isPlay = ref(false)
  let video = ref(null)
  const images = [
    {id: 0, name: 'https://cdn.aieasyart.com/easyart/images/image1.png'},
    {id: 1, name: 'https://cdn.aieasyart.com/easyart/images/image2.png'},
    {id: 2, name: 'https://cdn.aieasyart.com/easyart/images/image3.png'},
    {id: 3, name: 'https://cdn.aieasyart.com/easyart/images/image4.png'},
    {id: 4, name: 'https://cdn.aieasyart.com/easyart/images/image5.png'},
  ]
 
  const value = ref('产品设计')
  const segmentedArr = ['产品设计', '家居设计', '艺术设计']
  const beforeImage = ref('https://cdn.aieasyart.com/easyart/images/cpsj1.jpg')
  const afterImage = ref('https://cdn.aieasyart.com/easyart/images/cpsj2.jpg')
  const handleSegmentedClick = (item) => {
    if (item === '产品设计') {
      beforeImage.value = 'https://cdn.aieasyart.com/easyart/images/cpsj1.jpg'
      afterImage.value = 'https://cdn.aieasyart.com/easyart/images/cpsj2.jpg'
    } else if (item === '家居设计') {
      beforeImage.value = 'https://cdn.aieasyart.com/easyart/images/jjsj1.jpg'
      afterImage.value = 'https://cdn.aieasyart.com/easyart/images/jjsj2.jpg'
    } else if (item === '艺术设计') {
      beforeImage.value = 'https://cdn.aieasyart.com/easyart/images/yssj1.jpg'
      afterImage.value = 'https://cdn.aieasyart.com/easyart/images/yssj2.jpg'
    }
  }

  const beforeEl = ref(null)
  const sliderEl = ref(null)
  const handleSelect = (e) => {
    sliderEl.value.style.left = e.layerX+ 'px'
    beforeEl.value.style.clipPath = `polygon(0% 0%, ${e.layerX}px 0%, ${e.layerX }px 100%, 0% 100%)`
    e.stopPropagation()
  }

  const isShowSmallSlogan = ref(false)
  onMounted(() => {
    sliderEl.value.style.left = '50%'
    beforeEl.value.style.clipPath = `polygon(0% 0%, 50% 0%, 50% 100%, 0% 100%)`

    const wow = new WOW({
      boxClass: "wow",
      animateClass: "animated",
      offset: 0,
      mobile: true,
      live: true,
      callback: function () {
      },
      scrollContainer: null,
      resetAnimation: true,
    });
    wow.init();

    setTimeout(() => {
      isShowSmallSlogan.value = true
    }, 5000)
  })
  const setProgress = () => {
    let interval = setInterval(() => {
      percentage.value += 1
      if (percentage.value >= 97){
        clearInterval(interval)
      }
    }, 2000)
  }
  const handleExperienceNowClick = () => {
    if(inputValue.value === '') {
      ElMessage.warning('请输入内容！')
      return
    }
    if(isImgToImg.value) {
      uploadParentRef.value.submitUpload() // 图生图
    }  else {
      // 文生图
      let params = {
        prompt: inputValue.value,
        action: 'generate'
      }
      if(isLoading.value) {
        return
      }
      percentage.value = 0
      isLoading.value = true
      let interval = setInterval(() => {
        percentage.value += 3
        if (percentage.value >= 80){
          clearInterval(interval)
          setProgress()
        }
      }, 1000)
      axios.post('https://www.easyart.cc/prod-api/mj/txt2img', params).then((data) => {
        isLoading.value = false
        if(data && data.data && data.data.code === 200) {
          percentage.value = 100
          iamgeInfo.push(data.data.data.image_url)
        } else {
          ElMessage.error(data.data.msg)
        }
      }).catch((err) => {
        console.log(err)
        isLoading.value = false
      })
    }
  }
  // 图生图成功回调
  const uploadSuccess = (response) => {
    if(response && response.code === 200) {
      iamgeInfo.push(response.data.image_url)
    } else {
      ElMessage.error(response.msg)
    }
  }
  // 标记是否图生图
  const updateImageInfo = (flag) => {
    isImgToImg.value = flag
  }

  const handleInput = (val) => {
    inputValue.value = val
  }

  const pauseVideo = () => {
    isPlay.value = true
    video.value.muted = false
  }
  const playVideo = () => {
    isPlay.value = false
    video.value.muted = true
  }

  const scrollTo = () => {
    window.scrollBy({
      top: window.innerHeight,
      behavior: 'smooth'
    });
  }

  const handlePlateClick = (plate) => {
    router.push({path: plate})
  }
  
  const plateList = [
    {
      key: 'digitalIndustry',
      imgUrl: 'https://cdn.aieasyart.com/easyart/images/szcy.jpg',
      desc: '利用尖端AI技术，为企业量身定制解决方案。综合应用自主研发工具与数字资源，以优化业务模式，促进智能化转型，最终解决企业实际问题。',
    },
    {
      key: 'digitalEducation',
      imgUrl: 'https://cdn.aieasyart.com/easyart/images/szjy.jpg',
      desc: '结合学校教育需求与现有资源，利用AI技术赋能教育工作，提供个性化的教育平台和课程设计，以优化创新教学形式，助力培养未来高质创新人才。',
    },
    {
      key: 'digitalArt',
      imgUrl: 'https://cdn.aieasyart.com/easyart/images/szys.jpg',
      desc: '创新艺术设计，通过AI技术辅助设计和创作手段，实现多业态、多场景数字媒体表现形式的多样化，拓展数字艺术领域的无限可能。',
    },
  ]

</script>

<template>
  <div class="home-container">
    
    <div class="video-wapper">
      <video class="home-video" src="https://cdn.aieasyart.com/easyart/video/home_h264.mp4" controls autoplay muted loop ref="video"></video>
      <img :src="MuteIcon" alt="" class="MuteIcon" v-if="!isPlay" @click="pauseVideo"/>
      <img :src="PlayIcon" alt="" class="PlayIcon" v-if="isPlay" @click="playVideo"/>
      <div class="mask-wapper"></div>
      <div class="slogan" v-if="!isShowSmallSlogan">
        <div class="slogan-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">爱简艺</div>
        <div class="slogan-subtitle wow animate__animated animate__fadeInDown" data-wow-duration="2s">AIGC+内容生产综合服务商</div>
      </div>
      <div class="slogan-small wow animate__animated animate__fadeInDown" data-wow-duration="2s" v-else>爱简艺，AIGC+内容生产综合服务商</div>
      <div class="arrowDown" @click="scrollTo">
        <el-icon><ArrowDown /></el-icon>
      </div>
    </div>

    <div class="bg-wapper">
      <div class="bg-image"></div>
      <div class="page-title wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">Easy Art</div>
      <div class="page-subtitle wow animate__animated animate__fadeInRight" data-wow-duration="2s">用新一代的AI技术焕新不同的设计需求</div>
      <div class="items-start wow animate__animated animate__fadeInRight" data-wow-duration="2s">
        <el-segmented v-model="value" :options="segmentedArr" size="large" @change="handleSegmentedClick" />
      </div>
      <div class="image-animation wow animate__animated animate__fadeInLeft" data-wow-duration="2s">
        <div class="slider" ref="sliderEl"></div>
        <div class="before" ref="beforeEl">
          <img :src="beforeImage" alt="" ref="beforeImg" @mousemove="(e) => handleSelect(e)"/>
        </div>
        <div class="after">
          <img :src="afterImage" alt="" @mousemove="(e) => handleSelect(e)"/>
        </div>
      </div>
    </div>

    <div class="bg-wapper1">
      <div class="bg-image1"></div>
      <div class="first-ai-title wow animate__animated animate__fadeInDown" data-wow-duration="2s">开始首个 AI 作品</div>
      <div class="first-ai-subtitle wow animate__animated animate__fadeInDown" data-wow-duration="1s">没有思路？可以试试输入一段文字或上传一张图片，点击立即体验。</div>
      <div class="first-ai-content">
        <div class="upload-wrapper">
          <Upload @uploadSuccess="uploadSuccess" ref="uploadParentRef" @updateImageInfo="updateImageInfo" :inputValue="inputValue" />
          <div class="upload-text">
            <el-input v-model="inputValue" placeholder="请输入一段文字" :rows="10" resize="none" type="textarea" @input="handleInput" />
          </div>
        </div>
        <div class="result-wrapper">
          <el-progress :percentage="percentage":duration="6" v-if="isLoading"/>
          <el-image
            v-for="(item, index) in iamgeInfo"
            :key="index"
            style="width: 2.5rem; height: 2.5rem;"
            :src="iamgeInfo[index]"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="iamgeInfo"
            :z-index="index"
            :initial-index="index"
            fit="cover"
          />
        </div>
      </div>
      <div class="experienceNow-btn">
        <div @click="handleExperienceNowClick" class="experience-now wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">立即体验</div>
        <a class="experience-now openLink wow animate__animated animate__fadeInUp" data-wow-duration="1.5s" href="https://aieasyart.com/generation" target="_blank">
          体验完整版
          <el-icon><Right /></el-icon>
        </a>
      </div>
    </div>

    <div class="bg-wrapper4">
      <div class="bg-image4"></div>
      <div class="big-title wow animate__animated animate__fadeIn" data-wow-duration="2s">
        爱简艺服务板块
      </div>
      <div class="plate-sub-container wow animate__animated animate__fadeIn" data-wow-duration="2s">
        爱简艺是一家提供“人工智能+数字应用解决方案的综合服务商”。我们致力于通过结合人工智能技术与整合海量优质艺术资源，为细分行业、企业及具体场景数字化产业建设提供一站式的解决方案；为高等院校人工智能及相关专业建设提供产教融合解决方案；为园区、楼宇、其他公建项目提供数字化艺术作品及相关艺术设计方案。
      </div>
      <div class="plate-container">
        <div class="plate-item wow animate__animated animate__zoomIn" data-wow-duration="2s" @click="handlePlateClick(item.key)" v-for="(item, index) in plateList" :key="index">
          <div class="plate-icon">
            <img v-lazy="item.imgUrl" alt="" class="plate-icon-img" />
          </div>
          <div class="plate-desc">{{item.desc}}</div>
          <div class="service-cases">
            <div class="case-btn">查看服务案例<el-icon><TopRight /></el-icon></div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-wrapper2">
      <div class="bg-image2"></div>
      <div class="big-title wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
        模型本地化部署及定制
      </div>
      <div class="small-title wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">广东爱简艺提供全面的AI大模型定制服务，从语料到部署，助力企业数字化创新。</div>
      <div class="community-title wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">我们提供两种定制方案：全面解决方案和单次训练服务，以灵活适应不同客户的需求。全面解决方案涵盖深度语料采集与处理、模型结构优化、迭代训练和高效部署，确保模型与应用场景的精准匹配。爱简艺凭借在产品设计、艺术设计和场景渲染等领域的专业经验，助力客户将创意从概念转化为现实，打造品牌独有的数字体验。我们专注于提供高质量、个性化和创新的服务，为各行业带来先进的AI技术支持，推动企业智能化进程。</div>
      <div class="community-iamge wow animate__animated animate__zoomIn" data-wow-duration="2s">
        <div class="image-item">
          <img src="@/assets/bg/icon1.png" alt="" class="image" />
          <div class="text">对接需求明确输入输出</div>
        </div>
        <div class="image-item">
          <img src="@/assets/bg/icon2.png" alt="" class="image" />
          <div class="text">模型训练与调优</div>
        </div>
        <div class="image-item">
          <img src="@/assets/bg/icon3.png" alt="" class="image" />
          <div class="text">测试交付上线</div>
        </div>
      </div>
    </div>

    <div class="bg-wrapper5">
      <div class="bg-image5"></div>
      <CollaborationConsultation></CollaborationConsultation>
    </div>

    <!-- <div class="bg-wrapper3" id="cases" name="cases">
      <div class="fuwu-title wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">爱简艺服务案例</div>
      <div class="fuwu-sub-title wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">公司客户肖像覆盖各地政府、各类高校、制造业、文旅项目、公共艺术及AI数字媒体等领域。</div>
      <div class="swiper-container">
        <InfiniteScroll  :content="caseList" :direction="left">
          <template v-slot="{ item}">
            <div v-if="item.images && item.images.length === 1" class="iamge1">
              <img :src="item.images[0].name" alt="image" class="image" />
            </div>
            <div v-if="item.images && item.images.length === 2"  class="iamge2">
              <img :src="ite.name" alt="" v-for="(ite, index) in item.images" :key="index" class="image" />
            </div>
            <div v-if="item.images && item.images.length === 3"  class="iamge3">
              <img :src="ite.name" alt="" v-for="(ite, index) in item.images" :key="index" class="image" />
            </div>
            <div class="loop-type">
              {{item.title}}
            </div>
            <div class="loop-title">
              {{item.subTitle}}
            </div>
            <div class="loop-sub-title">
              {{item.text}}
            </div>
          </template>
        </InfiniteScroll>
      </div>
    </div> -->

  </div>
  
</template>

<style scoped lang="less">
.home-container {
  width: 100%;
  position: relative;
  .to-community {
    position: absolute;
    bottom: 5.4rem;
    left: 4.19rem;
    cursor: pointer;
    z-index: 20;
    .to-community-img {
      width: 100%;
      height: 100%;
    }
  }
  .bg-wapper {
    width: 100%;
    padding-top: 0.6rem;
    padding-bottom: 1rem;
    position: relative;
    .page-title {
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.76rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      
    }
    .page-subtitle {
      font-family: Noto Sans SC;
      font-weight: 500;
      font-size: 0.38rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-bottom: 0.4rem;
    }
    .items-start {
      display: flex;
      align-items: center;
      justify-content: center;
      /deep/ .el-segmented {
        width: 10rem;
        height: 0.54rem;
        padding: 0px;
        color: #FFFFFF;
        border: 1px solid rgba(255,255,255,0.48);
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        --el-segmented-item-selected-color: #fff;
        --el-segmented-item-selected-bg-color: none;
        --el-segmented-item-selected-bg: #fff;
        --el-border-radius-base: 48px;
        .is-selected {
          background: #fff;
          color: #000;
          background-size: 100% 100% !important;
        }
        .el-segmented__item:not(.is-disabled):not(.is-selected):hover {
          background: none !important;
          color: #fff !important;
        }
      }
      /deep/ .el-segmented--large {
        font-size: 0.24rem;
      }
    }
    .image-animation {
      width: var(--width-value-div);
      height: 8rem;
      margin: 0.6rem auto auto auto;
      box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.3);
      border-radius: 16px 16px 16px 16px;
      position: relative;
      overflow: hidden;
      user-select: none;
      .slider {
        width: 4px;
        background-color: #fff;
        border-radius: 8px;
        height: 100%;
        position: absolute;
        top: 0px;
        z-index: 4;
        cursor: pointer;
        user-select: none;
      }
      .before {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        overflow: hidden;
        border-radius: 16px;
        z-index: 2;
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          -o-object-fit: cover;
          object-fit: cover;
          overflow: hidden;
          -webkit-transition: all .1s ease;
          transition: all .1s ease;
        }
      }
      .after {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        overflow: hidden;
        border-radius: 16px;
        z-index: 1;
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          -o-object-fit: cover;
          object-fit: cover;
          overflow: hidden;
          -webkit-transition: all .1s ease;
          transition: all .1s ease;
        }
      }
    }
  }
  .bg-wapper1 {
    width: 100%;
    height: 12.5rem;
    position: relative;
    padding-top: 1.2rem;
    padding-bottom: 1.2rem;
    .first-ai-title {
      width: var(--width-value-div);
      margin-top: 1.2rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 0 auto;
    }
    .first-ai-subtitle {
      font-family: Noto Sans SC;
      font-size: 0.28rem;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      width: var(--width-value-div);
      margin: 0.35rem auto 0.4rem auto;
    }
    .first-ai-btns {
      display: flex;
      width: var(--width-value-div);
      margin: 0 auto 0.5rem auto;
      .first-ai-btn {
        cursor: pointer;
        width: 1.5rem;
        height: 0.45rem;
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
        border-radius: 26px;
        border: 1px solid rgba(255,255,255,0.48);
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.18rem;
        color: #FFFFFF;
        line-height: 0.42rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-right: 0.2rem;
      }
    }
    .first-ai-content {
      width: var(--width-value-div);
      height: 6.7rem;
      border: 1px solid #fff;
      border-radius: 0.1rem;
      overflow-y: auto;
      margin: 0 auto;
      .upload-wrapper {
        margin: 0.4rem;
        display: flex;
        align-items: center;
        .upload-text {
          width: 50%;
          height: 2.7rem;
          margin-top: -0.07rem;
          background: rgba(0, 0, 0, 0.7);
          border: 1px dashed #fff;
          border-radius: 0.06rem;
          box-sizing: border-box;
          cursor: pointer;
          overflow: hidden;
          margin-left: 0.24rem;
          margin-top: 0.02rem;
          &:hover {
            border: 1px dashed #fff;
          }
          /deep/ .el-textarea__inner {
            background: rgba(0, 0, 0, 0.7);
            box-shadow: none;
            color: #FFFFFF;
            padding: 0.1rem 0.11rem;
            font-size: 0.18rem;
          }
        }
      }
      .result-wrapper {
        margin: 0.3rem 0.4rem;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        width: auto;
        .el-progress {
          width: 100%;
        }
        .el-image {
          width: 3rem;
          height: 3rem;
          margin-right: 0.2rem;
          margin-bottom: 0.2rem;
        }
      }
    }
    .experienceNow-btn {
      margin: 0 auto;
      width: var(--width-value-div);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.6rem;
      .experience-now {
        color: rgb(255, 255, 255);
        width: 2.26rem;
        height: 0.56rem;
        line-height: 0.56rem;
        padding: 0px 0.16rem;
        margin-right: 0.4rem;
        border: 1px solid #fff;
        border-radius: 0.1rem;
        font-size: 0.20rem;
        letter-spacing: 0.02rem;
        font-weight: 600;
        cursor: pointer;
        text-align: center;
        &:hover {
          transform: scale(1.03);
        }
      }
      .openLink {
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
        .el-icon {
          margin-left: 0.08rem;
          font-size: 0.24rem;
          transition: transform 0.3s ease-in-out;
        }
        &:hover {
          .el-icon {
            transform: translateX(0.04rem);
          }
        }
      }
      
    }
  }
  .bg-wrapper2 {
    width: 100%;
    height: 12.5rem;
    position: relative;
    padding-top: 1.5rem;
    padding-bottom: 1.8rem;
    .big-title {
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      letter-spacing: 4px;
      margin-bottom: 0.4rem;
    }
    .small-title {
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.28rem;
      // color: #AEAEAE;
      text-align: center;
      font-style: normal;
      text-transform: none;
      letter-spacing: 4px;
      margin-bottom: 0.4rem;
    }
    .community-title {
      width: 60%;
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.24rem;
      // color: #AEAEAE;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 0 auto;
      letter-spacing: 1px;
      line-height: 0.4rem;
    }
    .community-iamge {
      width: var(--width-value-div);
      margin: 0.8rem auto 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      .image-item {
        width: 4.5rem;
        height: 4.5rem;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background: linear-gradient(230.94deg, #e5fbfb 8.02%, #fffcf3 100%);
        margin-right: 0.3rem;
        border-radius: 0.1rem;
        .image {
          width: 2.5rem;
        }
        .text {
          font-family: Noto Sans SC;
          font-weight: 600;
          font-size: 0.2rem;
          color: rgba(0, 0, 0, 1);
          text-align: center;
          font-style: normal;
          text-transform: none;
          letter-spacing: 0px;
          margin-top: 0.1rem;
        }
      }
    }
  }
  .bg-wrapper4 {
    width: 100%;
    height: 12.5rem;
    position: relative;
    padding-top: 1.2rem;
    .big-title {
      width: 70%;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.5rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
      letter-spacing: 4px;
      margin: 0 auto 0.5rem auto;
    }
    .plate-sub-container {
      width: 60%;
      font-family: Noto Sans SC;
      font-size: 0.28rem;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      margin: 0 auto 1rem auto;
      letter-spacing: 0.02rem;
    }
    .bm {
      margin: 0 auto 0.1rem auto;
    }
    .plate-container {
      display: flex;
      width: 70%;
      margin: 0 auto;
      .plate-item {
        width: 33%;
        height: auto;
        margin-right: 0.4rem;
        background: #020b1a;
        border: solid 1px #333;
        border-radius: 0.1rem;
        cursor: pointer;
        &:last-child {
          margin-right: 0;
        }
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        transition: all .2s linear;
        &:hover {
          transform: translateY(-10px);
        }
        .plate-icon {
          flex: 1;
          width: 100%;
          height: 80%;
          .plate-icon-img {
            width: 100%;
            height: 100%;
            border-top-left-radius: 0.1rem;
            border-top-right-radius: 0.1rem;
          }
        }
        .plate-desc {
          height: 21%;
          margin: 0.4rem 0.3rem;
          font-family: Noto Sans SC;
          font-size: 0.2rem;
          color: #fff;
          text-align: left;
          font-style: normal;
          text-transform: none;
          letter-spacing: 4px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 4;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .service-cases {
          font-family: Noto Sans SC;
          font-size: 0.18rem;
          color: #fff;
          width: 100%;
          text-align: center;
          height: 0.5rem;
          line-height: 0.5rem;
          margin-bottom: 0.4rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .case-btn {
            width: 1.8rem;
            border: solid 1px #474545;
            border-radius: 0.05rem;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover {
              border: solid 1px #fff;
            }
            .el-icon {
              margin-left: 0.08rem;
            }
          }
        }
      }
    }
  }
  .bg-wrapper5 {
    width: 100%;
    height: 12.5rem;
    position: relative;
    /deep/ .collaboration-consultation {
      .consultation-content {
        background: none;
        box-shadow: none;
        border: none;
        border-image: none;
      }
    }
  }
  .video-wapper {
    width: 100%;
    height: 100vh;
    position: relative;
    .mask-wapper {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
    }
    .slogan {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 999;
      width: 100%;
      font-family: Noto Sans SC;
      font-weight: 700;
      color: #fff;
      .slogan-title {
        font-size: 1.4rem;
        animation-name: focus-in-contract;
        animation-duration: 1s;
        animation-timing-function: linear;
        animation-delay: 0s;
        animation-iteration-count: 1;
        animation-direction: normal;
        animation-fill-mode: none;
      }
      .slogan-subtitle {
        font-size: 1rem;
        letter-spacing: 0.02rem;
        animation-name: focus-in-contract;
        animation-duration: 1s;
        animation-timing-function: ease-in-out;
        animation-delay: 0s;
        animation-iteration-count: 1;
        animation-direction: normal;
        animation-fill-mode: none;
      }
      @keyframes focus-in-contract {
        0% {
          letter-spacing:0.3rem;
          filter:blur(0.08rem);
          opacity:0;
        }
        100% {
          filter:blur(0);
          opacity:1;
        }
      }
    }
    .slogan-small {
      position: absolute;
      bottom: 0.9rem;
      text-align: center;
      z-index: 999;
      width: 100%;
      font-family: Noto Sans SC;
      letter-spacing: 0.02rem;
      font-size: 0.65rem;
      color: #fff;
    }
  
    .arrowDown {
      position: absolute;
      bottom: 0.2rem;
      left: 50%;
      transform: translateX(-50%);
      animation: jump 1s infinite;
      z-index: 999;
      cursor: pointer;
      .el-icon {
        font-size: 0.6rem;
      }
    }
    @keyframes jump {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(10px); }
    }
    .home-video {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
    .MuteIcon {
      position: absolute;
      bottom: 0.8rem;
      right: 1rem;
      z-index: 1000;
      width: 1rem;
      height: 1rem;
      cursor: pointer;
      border: solid 1px #fff;
      border-radius: 50%;
      padding: 10px;
    }
    .PlayIcon {
      position: absolute;
      bottom: 0.8rem;
      right: 1rem;
      z-index: 1000;
      width: 1rem;
      height: 1rem;
      cursor: pointer;
      border: solid 1px #fff;
      border-radius: 50%;
      padding: 10px;
    }
    video::-webkit-media-controls {
      display: none !important;
    }
  }

  .bg-image {
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // opacity: 0.4;
    z-index: -1;
  }
  .bg-image1 {
    background-image: url('@/assets/bg/bg1.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // opacity: 0.4;
    z-index: -1;
  }
  .bg-image4 {
    background-image: url('@/assets/bg/bg5.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    //opacity: 0.6;
    z-index: -1;
  }
  .bg-image2 {
    background-image: url('@/assets/bg/bg5.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // opacity: 0.6;
    z-index: -1;
  }
  .bg-image5 {
    background-image: url('@/assets/bg/bg4.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // opacity: 0.6;
    z-index: -1;
  }
}

</style>
