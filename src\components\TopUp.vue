<template>
  <div class="top-up" @click="handleClick">
    <el-icon :size="30">
      <Top />
    </el-icon>
  </div>
</template>
<script setup>
  import {onMounted} from 'vue'
  const handleClick = () => {
    window.scrollTo({ top: 0, behavior: 'smooth'});
  };
  onMounted(() => {
    window.addEventListener('scroll', () => {
      const topUp = document.querySelector('.top-up');
      if (window.scrollY > 300) {
        topUp.style.display = 'block';
      } else {
        topUp.style.display = 'none';
      }
    });
  })
</script>
<style scoped lang="less">
.top-up {
  display: none;
  position: fixed;
  right: 40px;
  bottom: 60px;
  z-index: 999;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  font-size: 22px;
  text-align: center;
  cursor: pointer;
  background-color: rgb(20, 20, 20);
  box-shadow: 0px 0px 0px 4px rgba(180, 160, 255, 0.253);
  .el-icon {
    margin-top: 10px;
  }
}
</style>