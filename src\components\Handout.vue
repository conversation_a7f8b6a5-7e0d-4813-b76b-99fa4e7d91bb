<template>
  <div class="handout-warpper">
    <div class="list-title">课程</div>
    <el-collapse v-model="activeNames">
      <el-collapse-item :title="item.title" :name="item.id" v-for="(item, index) in handoutList" :key="index">
        <div class="list-item-content" v-for="(itm, ind) in item.content" :key="ind"  @click="handleChange(itm)">
          {{ind+1}}. {{ itm.name }}
          <div class="type">
            <img class="typeicon" src="@/assets/image/pdf.png" alt="pdf" v-if="itm.type1 === 'PDF'"/>
            <img class="typeicon" src="@/assets/image/text.png" alt="doc" v-if="itm.type1 === '讲义'"/>
            <img class="typeicon" src="@/assets/image/video.png" alt="ppt" v-if="itm.type1 === '视频'"/>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <LoginDialog :isShow="isShowLogin" @closeDialog="closeDialog" />
  </div>
</template>

<script setup name="Handout">
  import { ref, onMounted, defineProps  } from 'vue';
  import {useRoute} from 'vue-router';
  import LoginDialog from '@/components/LoginDialog.vue';
  const props = defineProps(['actives'])
  let activeNames = ref([1,2,3])
  let route = useRoute()
  let pageIndex = route.query.index

  let isShowLogin = ref(false);

  const closeDialog = () => {
    isShowLogin.value = false;
  }

  const handoutList = ref([])

  onMounted(() => {
    if (props.actives) {
      activeNames.value = props.actives
    }
    if(localStorage.getItem('studentInfo')) {
      let data = JSON.parse(localStorage.getItem('studentInfo'))
      let temp = []
      data.forEach((item, index) => {
        if( pageIndex == item.id && item.textbook && item.textbook.length > 0) {
          temp.push(...item.textbook)
        }
      });
      let temp1 = temp.filter((item) => { return item.type === '入门篇' })
      let temp2 = temp.filter((item) => { return item.type === '进阶篇' })
      let temp3 = temp.filter((item) => { return item.type === '高级篇' })

      temp1.length > 0 && handoutList.value.push({ id: 1, title: '入门篇', content: temp1 })
      temp2.length > 0 && handoutList.value.push({ id: 2, title: '进阶篇', content: temp2 })
      temp3.length > 0 && handoutList.value.push({ id: 3, title: '高级篇', content: temp3 })
    }
  })

  const handleChange = (item) => {
    let token = localStorage.getItem('token')
    if (!token) {
      isShowLogin.value = true;
      return
    }
    window.open(item.url, '_blank')
  }
</script>

<style scoped lang="less">
  .handout-warpper {
    width: 100%;
    padding: 0.28rem;
    min-height: 5.5rem;
    background: rgba(17,17,17,0.5);
    border-radius: 12px 12px 12px 12px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(110, 110, 110, 1), rgba(45, 45, 45, 1)) 1 1;
    .list-title {
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.26rem;
      color: #FFFFFF;
      line-height: 0.24rem;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 0.26rem;
    }
    /deep/ .el-collapse {
      .el-collapse-item__content {
        background: none !important;
        padding: 0px !important;
        .list-item-content {
          padding: 0.22rem;
          background: #333 !important;
          margin-bottom: 0.14rem;
          font-family: Noto Sans SC;
          font-weight: 500;
          font-size: 0.2rem;
          color: #FFFFFF;
          line-height: 0.24rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
          cursor: pointer;
          position: relative;
          .type {
            position: absolute;
            top: 0.19rem;
            right: 0.3rem;
            .typeicon {
              width: 0.3rem;
              height: 0.3rem;
            }
          }
        }
      }
    }
  }
</style>