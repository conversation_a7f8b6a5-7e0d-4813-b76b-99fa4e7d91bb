<template>
  <div class="art-community">
    <div class="art-community-style">
      <div class="style-image wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
        <img v-if="styleIndex === '0'" src="https://cdn.aieasyart.com/easyart/images/style2.jpg" alt="" class="style-img-item"/>
        <img v-else-if="styleIndex === '1'" src="https://cdn.aieasyart.com/easyart/images/style1.jpg" alt="" class="style-img-item"/>
        <img v-else-if="styleIndex === '2'" src="https://cdn.aieasyart.com/easyart/images/style3.jpg" alt="" class="style-img-item"/>
        <img v-else-if="styleIndex === '3'" src="https://cdn.aieasyart.com/easyart/images/style4.jpg" alt="" class="style-img-item"/>
        <img v-else-if="styleIndex === '4'" src="https://cdn.aieasyart.com/easyart/images/style5.jpg" alt="" class="style-img-item"/>
        <img v-else-if="styleIndex === '5'" src="https://cdn.aieasyart.com/easyart/images/style6.jpg" alt="" class="style-img-item"/>
      </div>
      <div class="style-tab wow animate__animated animate__fadeInRight" data-wow-duration="1.5s">
        <div :class="tabIndex === index ? 'tab-item active' : 'tab-item'" v-for="(item, index) in styleList" :key="index" @click="changeStyle(item.id)">{{item.name}}</div>
      </div>
    </div>

    <div class="community-description wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
      <div class="title">欢迎来到「爱简艺」AI艺术社区，</div>
      <div class="title">一个集学习、交流、分享于一体的学习平台，</div>
      <div class="description">在这里，你可以获取到AI行业的最新动态与学习资讯，期待与您共探AI之旅！</div>
    </div>
    
    <div class="select-type">
      <el-segmented v-model="value" :options="options" size="large" @change="changeType" />
    </div>
    <div class="select-type-content">
      <div class="select-type-text wow animate__animated animate__fadeInLeft" data-wow-duration="1.5s">
        <div v-if="tabType !== 'AI艺术家作品'">{{tabValue}}</div>
        <div class="select-type-text-span" v-if="tabType === 'AI艺术家作品'">
          <div>作品名称：《上上签—火焰之舞》</div>
          <div>作者：陆平原</div>
          <div>材料：无酸水彩纸、蜡光纸、着色卡纸、着色雪梨纸、丙烯、水彩、无酸胶</div>
          <div>尺寸：150 × 200 cm</div>
          <div>年份：2024</div>
        </div>
      </div>
      <div class="select-type-img wow animate__animated animate__fadeInUp" data-wow-duration="1.5s">
        <img v-if="tabType === 'AI资讯'" src="https://cdn.aieasyart.com/easyart/images/aizixun.jpg" alt="" class="imag"/>
        <img v-if="tabType === 'AI艺术家作品'" src="https://cdn.aieasyart.com/easyart/images/aizuopin.jpg" alt="" class="imag"/>
        <video v-if="tabType === 'AI绘画教程'" src="https://cdn.aieasyart.com/easyart/video/AIPainting.mp4" controls="controls" class="video"></video>
      </div>
    </div>

    <div class="chat-wapper">
      <div class="chat-left">
        <div class="chat-left-top" ref="infoScrollContainer">
          <div class="learning-experience__content" v-for="(item, index) in sendInfo" :key="index">
            <div class="user-info__content__item">
              <div class="user-header">
                <img :src="item.userImg" alt="class-img" class="img" />
              </div>
              <div class="user-name-date">
                <!-- <div class="user-name">{{item.userName}}</div> -->
                <div class="user-date">{{item.value}}</div>
              </div>
            </div>
            <!-- <div class="user-info__content__text">{{item.value}}</div>
            <div class="user-info__content__img" v-if="item.image">
              <img :src="item.image" alt="class-img" class="img" />
            </div> -->
          </div>
        </div>
        <div class="chat-left-bottom">
          <div class="input-content">
            <el-input v-model="sendValue" placeholder="请输入内容" rows="2" resize="none" type="textarea" />
          </div>
          <div class="send-btn" @click="handleSend">发 送</div>
        </div>
      </div>
      <div class="chat-right">
        <div class="chat-right-left">
          <div :class="typeIndex == index ? 'type-item active' : 'type-item'" v-for="(item, index) in typeList" :key="index" @click="handleType(item)">{{item.name}}</div>
        </div>
        <div class="chat-right-center"></div>
        <div class="chat-right-right">
          <div class="right-top">
            <div class="right-item" v-for="(item, index) in userList" :key="index" @click="handleUser(item)">
              <div :class="userIndex === index ? 'user-img active' : 'user-img'">
                <img :src="item.image" alt="" class="imag"/>
              </div>
              <div class="user-name">{{item.name}}</div>
            </div>
          </div>
          <div class="right-bottom">
            <div class="right-item">
              <div class="user-img">
                <img :src="addUserImage" alt="" class="imag"/>
              </div>
              <div class="user-name">添加艺术家</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="page-bg"></div>
</template>v 

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import userImg from '@/assets/image/class1.png'
  import userImg1 from '@/assets/image/userImg1.png'
  import userImg2 from '@/assets/image/userImg2.png'
  import userImg3 from '@/assets/image/class3.png'
  import addUserImage from '@/assets/image/addUser.png'

  let styleIndex = ref('5')
  let tabIndex = ref(5)
  let tabValue = ref('2024年9月16日，南加州大学的AI与艺术研讨会成功举办，蔡国强等专家深入探讨了AI在艺术领域的应用及其对人类文明的影响。会议强调，艺术的本质在于创新，其表现形式应当是多元化的。若艺术家因AI而失业，这不仅暗示着缺乏创新精神的艺术家可能被时代淘汰，也反映出艺术界需要与AI同步进化，利用这一技术拓宽创作视野，保持艺术的多元性。同时，如果AI能够取代人类，则表明文明迫切需要新的创新动力。因此，人类必须积极拥抱AI技术，实现自我革新，建立技术伦理，确保与AI的和谐共生，并重视教育改革与人才培养，以应对AI时代的挑战，保障艺术与人类文明的持续发展和繁荣。')
  let sendValue = ref('')
  let tabType = ref('AI资讯')
  let userIndex = ref(0)
  let typeIndex = ref(0)
  let sendInfo = reactive([])
  const infoScrollContainer = ref(null);
  const changeStyle = (id) => {
    styleIndex.value = id.toString()
    tabIndex.value = id
  }
  const value = ref('AI资讯')
  const options = ['AI资讯', 'AI艺术家作品', 'AI绘画教程', 'AI视频教程']

  const changeType = (item) => {
    tabType.value = item
    if (item === 'AI资讯') {
      tabValue.value = '2024年9月16日，南加州大学的AI与艺术研讨会成功举办，蔡国强等专家深入探讨了AI在艺术领域的应用及其对人类文明的影响。会议强调，艺术的本质在于创新，其表现形式应当是多元化的。若艺术家因AI而失业，这不仅暗示着缺乏创新精神的艺术家可能被时代淘汰，也反映出艺术界需要与AI同步进化，利用这一技术拓宽创作视野，保持艺术的多元性。同时，如果AI能够取代人类，则表明文明迫切需要新的创新动力。因此，人类必须积极拥抱AI技术，实现自我革新，建立技术伦理，确保与AI的和谐共生，并重视教育改革与人才培养，以应对AI时代的挑战，保障艺术与人类文明的持续发展和繁荣。'
    } else if (item === 'AI艺术家作品') {
      tabValue.value = ''
    } else if (item === 'AI绘画教程') {
      tabValue.value = 'AI绘画教程--奢侈品墨镜海报设计教程'
    } else if (item === 'AI视频教程') {
      tabValue.value = 'AI视频教程'
    }
  }

  const handleUser = (item) => {
    userIndex.value = item.id
  }
  const handleType = (item) => {
    typeIndex.value = item.id
    sendInfo = []
    getInfoList()
    setTimeout(() => {
      scrollToBottom(infoScrollContainer.value)
    })
  }

  const scrollToBottom = (element) =>{
    if (element) {
      element.scrollTop = element.scrollHeight;
    }
  }

  onMounted(() => {
    getInfoList()
    setTimeout(() => {
      scrollToBottom(infoScrollContainer.value)
    })

    setInterval(() => {
      changeStyle(parseInt((Math.random() * 5).toFixed(0)))
    }, 5000)
  })

  const getInfoList = () => {
    let key = 'noticeInfo'
    if(typeIndex.value === 0) {
      key = 'noticeInfo'
    } else if(typeIndex.value === 1) {
      key = 'activityInfo'
    } else if(typeIndex.value === 2) {
      key = 'chatInfo'
    }
    let data = JSON.parse(localStorage.getItem(key))
    if (data) {
      sendInfo.push(...data)
    } else {
      let data = [
        {
          value: '35岁能学AI设计吗？',
          // userName: '小明同学',
          userImg: userImg1,
          // image: key === 'noticeInfo' ? 'https://cdn.aieasyart.com/easyart/images/style2.jpg' : '',
          date: new Date().toLocaleString(),
        },
        {
          value: '我41岁了都在玩Easy Art的AI设计平台呢。',
          // userName: '小明同学',
          userImg: userImg2,
          // image: key === 'noticeInfo' ? 'https://cdn.aieasyart.com/easyart/images/style2.jpg' : '',
          date: new Date().toLocaleString(),
        },
        {
          value: '学的久吗？',
          // userName: '小明同学',
          userImg: userImg3,
          // image: key === 'noticeInfo' ? 'https://cdn.aieasyart.com/easyart/images/style2.jpg' : '',
          date: new Date().toLocaleString(),
        }
      ]
      sendInfo.push(...data)
    }
    return []
  }

  const handleSend = () => {
    let key = 'noticeInfo'
    if(typeIndex.value === 0) {
      key = 'noticeInfo'
    } else if(typeIndex.value === 1) {
      key = 'activityInfo'
    } else if(typeIndex.value === 2) {
      key = 'chatInfo'
    }
    sendInfo.push({
      value: sendValue.value,
      // userName: '小明同学',
      userImg: userImg,
      // image: key === 'noticeInfo' ? 'https://cdn.aieasyart.com/easyart/images/style2.jpg' : '',
      date: new Date().toLocaleString(),
    })
    sendValue.value = ''
    localStorage.setItem(key, JSON.stringify(sendInfo))
    scrollToBottom(infoScrollContainer.value)
  }

  const styleList = [
    {
      id: 0,
      name: '室内设计',
    },
    {
      id: 1,
      name: '艺术建筑',
    },
    {
      id: 2,
      name: '未来摄影',
    },
    {
      id: 3,
      name: '赛博朋克',
    },
    {
      id: 4,
      name: '国风',
    },
    {
      id: 5,
      name: '黑神话',
    }
  ]
  const typeList = [
    {
      id: 0,
       name: '公告',
     },
     {
      name: '活动',
    },
    {
      id: 2,
      name: '聊天',
    },
  ]
  const userList = [
    {
      id: 0,
      name: '用户1',
      image: userImg,
    },
    {
      id: 1,
      name: '用户2',
      image: userImg,
    },
    {
      id: 2,
      name: '用户3',
      image: userImg,
    },
  ]

</script>

<style scoped lang="less">
  .art-community {
    width: 14rem;
    margin: 0 auto;
    padding: 1.5rem 0 1rem 0;
    .art-community-style {
      display: flex;
      margin-bottom: 1.5rem;
      .style-image {
        flex: 1;
        height: 6rem;
        margin-right: 0.34rem;
        position: relative;
        .style-bg {
          width: 100%;
          height: 100%;
        }
        .style-img-item {
          width: 100%;
          height: 100%;
          border-radius: 0.1rem;
          object-fit: cover;
        }
      }
      .style-tab {
        width: 25%;
        height: 6rem;
        background: rgba(17,17,17,0.5);
        border-radius: 0.1rem;
        border: 1px solid rgba(45, 45, 45, 1);
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 0.5rem;
        .tab-item {
          width: 2.5rem;
          height: 0.5rem;
          line-height: 0.5rem;
          text-align: center;
          font-size: 0.18rem;
          background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
          box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
          border-radius: 0.26rem;
          border: 1px solid rgba(255,255,255,0.48);
          margin-bottom: 0.3rem;
          cursor: pointer;
        }
        .active {
          background-image: linear-gradient(to right, #8e9eab 0%, #eef2f3  51%, #8e9eab  100%);
          transition: 0.5s;
          background-size: 200% auto;
          color: #000;
          &:hover {
            background-position: right center;
            color: #000;
            text-decoration: none;
          }
        }
      }
    }
    .community-description {
      font-family: Noto Sans SC;
      font-size: 0.26rem;
      color: rgba(255,255,255, 1);
      text-align: center;
      line-height: 0.44rem;
      .description {
      }
    }
    .select-type {
      margin-top: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      /deep/ .el-segmented {
        width: 100%;
        padding: 0px;
        height: 0.56rem;
        color: #FFFFFF;
        border: 1px solid rgba(255,255,255,0.48);
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        --el-segmented-item-selected-color: #fff;
        --el-segmented-item-selected-bg-color: none;
        --el-segmented-item-selected-bg: none !important;
        --el-border-radius-base: 48px;
        .is-selected {
          /* background: url('/src/assets/image/btnBg2.png') no-repeat !important;
          background-size: 100% 100% !important;
          border: solid 1px #fff; */
          background-image: linear-gradient(to right, #8e9eab 0%, #eef2f3  51%, #8e9eab  100%);
          transition: 0.5s;
          background-size: 200% auto;
          color: #000;
          &:hover {
            background-position: right center;
            color: #000;
            text-decoration: none;
          }
        }
        &.el-segmented--large {
          font-size: 0.2rem;
        }
        .el-segmented__item:not(.is-disabled):not(.is-selected):hover {
          background: none !important;
          color: #fff !important;
        }
      }
    }
    .select-type-content {
      margin-top: 0.5rem;
      display: flex;
      .select-type-text {
        flex: 1;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 0.22rem;
        color: rgba(255,255,255,0.7);
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-right: 0.3rem;
      }
      .select-type-img {
        width: 40%;
        height: 4.5rem;
        border-radius: 12px 12px 12px 12px;
        .imag {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px 12px 12px 12px;
        }
        .video {
          width: 100%;
          height: 100%;
        }
      }
    }
    .chat-wapper {
      width: 100%;
      display: flex;
      margin-top: 1.5rem;
      .chat-left {
        flex: 1;
        height: 6rem;
        padding: 0.3rem;
        margin-right: 0.4rem;
        background: rgba(17,17,17,0.5);
        border-radius: 0.12rem;
        border: 1px solid rgba(110, 110, 110, 1);
        .chat-left-top {
          width: 100%;
          height: 4.5rem;
          overflow-y: auto;
          padding-right: 0.1rem;
          .learning-experience__content {
            margin-bottom: 0.3rem;
            .user-info__content__item {
              display: flex;
              align-items: center;
              margin-bottom: 0.1rem;
              .user-header {
                width: 0.6rem;
                height: 0.6rem;
                border-radius: 50%;
                border: 2px solid transparent;
                background-image: linear-gradient(to right, rgba(202, 123, 255, 1), rgba(128, 251, 255, 1));
                margin-right: 0.1rem;
                .img {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                }
              }
              .user-name-date {
                .user-name {
                  font-family: Noto Sans SC;
                  font-weight: bold;
                  font-size: 0.14rem;
                  color: #FFFFFF;
                  line-height: 14px;
                  font-style: normal;
                  text-transform: none;
                  margin-bottom: 6px;
                }
                .user-date {
                  font-family: Noto Sans SC;
                  margin-left: 0.08rem;
                  font-weight: 400;
                  font-size: 0.2rem;
                  color: #FFFFFF;
                  font-style: normal;
                  text-transform: none;
                }
              }
              
            }
            .user-info__content__text {
              font-family: Noto Sans SC;
              font-weight: 400;
              font-size: 16px;
              color: #FFFFFF;
              line-height: 24px;
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-bottom: 0.1rem;
            }
            .user-info__content__img {
              width: 50%;
              height: 50%;
              border-radius: 0.1rem;
              .img {
                width: 100%;
                height: 100%;
                border-radius: 0.1rem;
                object-fit: cover;
              }
            }
          }
        }
        .chat-left-bottom {
          display: flex;
          align-items: center;
          .input-content {
            padding: 0.1rem;
            font-style: normal;
            .el-textarea {
              width: 3.7rem;
              --el-input-bg-color:rgba(17,17,17,0.5);
              --el-input-hover-border-color:#666;
              --el-input-focus-border-color:#666;
              --el-input-text-color: #FFFFFF;
              --el-input-placeholder-color: #ccc;
              --el-border-color: #363232;
              --el-input-icon-color: #FFFFFF;
            }
          }
          .send-btn {
            min-width: 1rem;
            height: 0.55rem;
            line-height: 0.55rem;
            border-radius: 0.28rem;
            border: 2px solid #484b4e;
            font-family: Noto Sans SC;
            font-weight: bold;
            font-size: 0.16rem;
            color: #FFFFFF;
            text-align: center;
            font-style: normal;
            text-transform: none;
            cursor: pointer;
            &:hover {
              border: 2px solid #94cfff;
            }
          }
        }
      }
      .chat-right {
        width: 28%;
        height: 6rem;
        background: rgba(17,17,17,0.5);
        border-radius: 0.12rem;
        border: 1px solid rgba(110, 110, 110, 1);
        display: flex;
        justify-content: center;
        .chat-right-left {
          padding-top: 0.3rem;
          padding-left: 0.3rem;
          .type-item {
            width: 1.8rem;
            height: 0.5rem;
            margin-bottom: 0.24rem;
            background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
            box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
            border-radius: 0.26rem;
            border: 1px solid rgba(255,255,255,0.48);
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 0.18rem;
            color: #FFFFFF;
            line-height: 0.5rem;
            text-align: center;
            font-style: normal;
            text-transform: none;
            cursor: pointer;
          }
          .active {
            background-image: linear-gradient(to right, #8e9eab 0%, #eef2f3  51%, #8e9eab  100%);
            transition: 0.5s;
            background-size: 200% auto;
            color: #000;
            &:hover {
              background-position: right center;
              color: #000;
              text-decoration: none;
            }
          }
        }
        .chat-right-center {
          width: 1px;
          height: 5.76rem;
          margin-right: 0.3rem;
          margin-left: 0.3rem;
          background: linear-gradient( 180deg, rgba(255,255,255,0.06) 0%, #1F1F1F 50%);
          border-radius: 0px 0px 0px 0px;
        }
        .chat-right-right {
          padding-top: 0.3rem;
          .right-top {
            height: calc(100% - 1.1rem);
            overflow-y: auto;
            .right-item {
              margin-bottom: 24px;
              cursor: pointer;
              .user-img {
                width: 0.65rem;
                height: 0.65rem;
                border-radius: 50%;
                margin-bottom: 0.08rem;
                border: 2px solid transparent;
                .imag {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                }
              }
              .active {
                background-image: linear-gradient(to right, #ca7bff, #80fbff);
              }
              .user-name {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.16rem;
                color: #FFFFFF;
                text-align: center;
                font-style: normal;
                text-transform: none;
              }
            }
          }
          .right-bottom {
            .right-item {
              cursor: pointer;
              .user-img {
                width: 0.65rem;
                height: 0.65rem;
                border-radius: 50%;
                margin-bottom: 0.08rem;
                border: 2px solid transparent;
                .imag {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                }
              }
              .user-name {
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 0.14rem;
                color: #FFFFFF;
                text-align: center;
                font-style: normal;
                text-transform: none;
              }
            }
          }
        }
      }
    }
  }
  .page-bg {
    background-image: url('@/assets/image/home-bg6.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.4;
    z-index: -1;
  }
</style>