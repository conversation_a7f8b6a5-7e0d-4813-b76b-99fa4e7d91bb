
<template>
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-left">
        <img src="@/assets/image/logo1.png" alt="logo" class="logo" />
        <div class="logo-text"><span class="logo-en">Easy Art</span> 爱简艺</div>
      </div>
      <div class="footer-right">
        <div class="footer-right-btn" @click="goEasyArt">AI设计平台 <img class="footer-right-img" src="@/assets/svg/footer-right.svg"/></div>
        <div class="footer-right-btn marginLeft24" @click="goCommunity">进入社区 <img class="footer-right-img" src="@/assets/svg/footer-right.svg"/></div>
      </div>
    </div>
    <div class="footer-copyright">
      <div class="er-code-container">
        <div class="er-code-img">
          <div class="er-code-img-title">公众号二维码</div>
          <div class="code-img-container">
            <img class="code-img" src="@/assets/image/qrcode-stay.png" alt="" />
          </div>
        </div>
        <div class="er-code-img">
          <div class="er-code-img-title">小红书二维码</div>
          <div class="code-img-container">
            <img class="code-img" src="@/assets/image/qrcode2.png" alt="" />
          </div>
        </div>
      </div>
      <div class="address-container">
        <div class="text">商务合作：<EMAIL></div>
        <div class="text">公司地址：广州市海珠区琶洲街道欧派国际广场1207</div>
      </div>
      <div class="copyright">
        <div class="text">copyright@广东爱简艺</div>
        <div class="text"><a href="https://beian.miit.gov.cn/#/Integrated/index" class="beian" target="_blank">粤ICP备2024223912号-2</a></div>
      </div>
    </div>
    <div class="footer-top-line"></div>
    <div class="footer-bottom-line"></div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';

  const goEasyArt = () => {
    window.open('https://aieasyart.com/', '_target')
  }
  const router = useRouter()
  const goCommunity = () => {
    router.push('/artCommunity')
  }
</script>

<style scoped lang="less">
.footer-container {
  //margin-top: 80px;
  height: 244px;
  padding-bottom: 20px;
  position: relative;
  width: 100%;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .footer-bottom-line {
    position: absolute;
    bottom: 158px;
    left: 0;
    width: 100%;
    border-bottom: 1px solid rgba(255,255,255,0.16); 
  }
  .footer-top-line {
    position: absolute;
    bottom: 238px;
    left: 0;
    width: 100%;
    border-top: 1px solid rgba(255,255,255,0.16);
  }
  .footer-content {
    width: 12rem;
    height: 80px;
    display: flex;
    align-items: center;
    .footer-left {
      flex: 1;
      display: flex;
      align-items: center;
      .logo {
        height: 45px;
        margin-right: 10px;
      }
      .logo-text {
        font-size: 16px;
        .logo-en {
          font-weight: 600;
        }
      }
    }
    .footer-right {
      display: flex;
      .footer-right-btn {
        width: 140px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        background: linear-gradient( 180deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 32%), rgba(255,255,255,0.04);
        box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.16);
        border-radius: 26px 26px 26px 26px;
        border: 1px solid rgba(255,255,255,0.48);
        .footer-right-img {
          margin-left: 14px;
        }
      }
    }
  }
  .footer-copyright {
    width: 12rem;
    margin-top: 0.16rem;
    display: flex;
    .er-code-container {
      display: flex;
      .er-code-img {
        margin-right: 14px;
        .er-code-img-title {
          font-family: Noto Sans SC;
          font-weight: 400;
          font-size: 16px;
          color: #666666;
          line-height: 14px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-bottom: 8px;
        }
        .code-img-container {
          width: 100px;
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          .code-img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .address-container {
      flex: 1;
      margin-left: 20px;
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      .text {
        font-family: Noto Sans SC;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .copyright {
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      .text {
        font-family: Noto Sans SC;
        font-weight: 600;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        .beian {
          font-family: Noto Sans SC;
          font-size: 14px;
          font-weight: 400;
          margin-left: 10px;
          color: #93938f;
          text-decoration: none;
          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>