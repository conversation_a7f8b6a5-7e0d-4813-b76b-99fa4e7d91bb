<template>
  <div class="company-profile">
    <div class="big-title wow animate__animated animate__fadeInDown" data-wow-duration="2s">公司简介</div>
    <div class="sub-title wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">广东爱简艺人工智能科技有限公司</div>
    <div class="sub-title-en wow animate__animated animate__fadeInDown" data-wow-duration="1s">Guangdong Easy Art Artificial Intelligence Tech.Co,Ltd</div>
    <div class="content-container firstLine">
      <div class="content-item right40 wow animate__animated animate__fadeInDown" data-wow-duration="1.5s">
        <div class="text1">广东爱简艺人工智能科技有限公司（ EasyArt，以下简称爱简艺 ）是一家提供“人工智能+数字应用”解决方案的综合服务商，通过结合人工智能技术与整合海量优质艺术资源，自研并开发了AI设计大模型—乌龙大模型
          （ <a href="https://aieasyart.com/" target="_blank" class="open-link">https://aieasyart.com/</a> ）【 国家网信办算法备案
          <a href="https://beian.cac.gov.cn/#/index" target="_blank" class="open-link">440105923031401240013号</a> 】。
          为高等院校人工智能及相关专业建设提供产教融合解决方案；为园区、楼宇、其他公建项目提供数字化艺术作品及相关艺术设计；为细分行业、企业及具体场景数字化产业建设提供一站式的解决方案。公司客户肖像覆盖各地政府、各类高校、制造业、文旅项目、公共艺术及AI数字媒体等领域。</div>
      </div>
    </div>

    <div class="content-container">
      <div class="content-img right40 wow animate__animated animate__fadeInLeft" data-wow-duration="2s">
        <img class="img" src="https://cdn.aieasyart.com/easyart/images/gs-szjy.jpg" alt="">
      </div>
      <div class="content-item wow animate__animated animate__fadeInRight" data-wow-duration="2s">
        <div class="text1">在数字教育领域，爱简艺为高等院校提供全球领先的GPU、服务器部署及硬件设备、国内领先的AI教学实训平台、人工智能师资及学生课程培训、职业技能培训、源于AI产业实践的实训案例等校企合作服务项目。目前与南方科技大学、广东财经大学等国内知名高校共建多维度、强实操的AI+产业课程，为各类高校提供产教融合解决方案及人才就业扶持。</div>
      </div>
    </div>

    <div class="content-container">
      <div class="content-item right40 wow animate__animated animate__fadeInLeft" data-wow-duration="2s">
        <div class="text1">在数字产业领域，爱简艺致力于运用人工智能技术解决行业实际问题，推动企业经营提质增效。通过AI+产业场景应用赋能企业，深入挖掘产业应用和企业需求，提供定制化的AI大模型研发和数据整合服务。同时，依托自主研发的AI获客系统和AI数字人等创新营销工具，我们为不同领域的企业、园区和楼宇运营商等提供高质量的发展解决方案及相应的软硬件配套支持。</div>
      </div>
      <div class="content-img wow animate__animated animate__fadeInRight" data-wow-duration="2s">
        <img class="img" src="https://cdn.aieasyart.com/easyart/images/gs-szcy.jpg" alt="">
      </div>
    </div>

    <div class="content-container">
      <div class="content-img right40 wow animate__animated animate__fadeInLeft" data-wow-duration="2s">
        <img class="img" src="https://cdn.aieasyart.com/easyart/images/gs-szys.jpg" alt="">
      </div>
      <div class="content-item wow animate__animated animate__fadeInRight" data-wow-duration="2s">
        <div class="text1">在数字艺术领域，爱简艺聚焦于AI城市灯光秀、AI视频制作、AI公共艺术、AI艺术设计策划展览等项目方面，深度探索AI艺术赋能各业态场景的无限可能，为艺术行业、媒体行业及各类关联单位赋能，以人工智能+艺术的垂直形式，积极为行业及城市构建良好的科技艺术应用生态。</div>
      </div>
    </div>
  </div>
  <div class="page-bg"></div>
</template>

<style scoped lang="less">
  .company-profile {
    width: 60%;
    margin: 0 auto;
    padding: 1rem 0 1rem 0;
    position: relative;
    .big-title {
      margin-top: 0.6rem;
      margin-bottom: 0.3rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.44rem;
      color: #FFFFFF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .sub-title {
      margin-bottom: 0.16rem;
      font-family: Noto Sans SC;
      font-weight: bold;
      font-size: 0.3rem;
      color: #FFFFFF;
      text-align: center;
      line-height: 0.28rem;
      font-style: normal;
      text-transform: none;
    }
    .sub-title-en {
      font-family: Noto Sans SC;
      font-weight: 400;
      font-size: 0.18rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
      line-height: 28px;
      font-style: normal;
      text-transform: none;
    }
    .content-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.9rem;
      .content-item {
        width: 43%;
        font-family: Noto Sans SC;
        font-size: 0.26rem;
        color: rgba(255,255,255, 1);
        text-align: left;
        .text1 {
          margin-bottom: 0.2rem; 
          line-height: 0.5rem;
          .open-link {
            color: #fff;
            cursor: pointer;
            text-decoration: none;
            &:hover {
              color: #4f61cd;
            }
          }
        }
      }
      .content-img {
        width: 53%;
        .img {
          width: 100%;
          height: 100%;
          border-radius: 0.1rem;
          object-fit: cover;
        }
      }
    }
    .firstLine {
      .content-item {
        width: 100%;
        .text1 {
          line-height: 0.5rem;
        }
      }
    }
    .content-container1 {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 80px;
    }
  }
  .page-bg {
    background-image: url('@/assets/bg/bg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.4;
    z-index: -1;
  }
</style>
