.easy-art-layout {
  width: 100%;
  position: relative;
  margin: 0 auto;
  height: 100%;
  width: 100%;
  .background-circle {
    position: absolute;
    top: 0%;
    left: 50%;
    transform: translate(-50%, 0%);
    width: 100%;
    height: 100%;
    z-index: -1;
    background: url('@/assets/svg/BG.svg') no-repeat;
    /* .line-left {
      position: absolute;
      left: calc(50% - 7.2rem);
      top: 0;
      width: 1px;
      height: 100%;
      background: linear-gradient( 180deg, rgba(255,255,255,0.1) 0%, #1F1F1F 30%);
    }
    .line-right {
      position: absolute;
      right: calc(50% - 7.2rem);
      top: 0;
      width: 1px;
      height: 100%;
      background: linear-gradient( 180deg, rgba(255,255,255,0.1) 0%, #1F1F1F 30%);
    }
    .line-left-short {
      position: absolute;
      left: calc(50% - 6rem);
      top: 0;
      width: 1px;
      height: 100%;
      max-height: 1100px;
      background: linear-gradient( 180deg, rgba(255,255,255,0.1) 0%, #1F1F1F 27%);
    }
    .line-right-short {
      position: absolute;
      right: calc(50% - 6rem);
      top: 0;
      width: 1px;
      height: 100%;
      max-height: 1100px;
      background: linear-gradient( 180deg, rgba(255,255,255,0.1) 0%, #1F1F1F 27%);
    } */
  }
  .easy-art-content {
    margin: -1rem auto auto auto;
    min-height: calc(100vh - 198px);
  }
  /* .line-bottom {
    position: absolute;
    left: 0;
    top: 0.73rem;
    width: 100%;
    border: 1px solid rgba(255,255,255,0.06);
  } */
  .bottom-circle {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
    .bottom-circle-img {
      width: 100%;
      height: 100%;
    }
  }
}
@media (max-width: 1640px) {
  /* .easy-art-layout{
    .header-warp {
      width: 9rem;
    }
    .easy-art-content {
      width: 9rem;
    }
    .footer-container {
      .footer-content, .footer-copyright {
        width: 9rem;
      }
    }
    .background-circle {
      .line-left {
        left: calc(50% - 5.7rem);
      }
      .line-right {
        right: calc(50% - 5.7rem);
      }
      .line-left-short {
        left: calc(50% - 4.5rem);
      }
      .line-right-short {
        right: calc(50% - 4.5rem);
      }
    }
  } */
}
.el-select__popper {
  background: #121212 !important;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.5) !important;
  border: solid 1px #4a4b4c !important;
  .el-select-dropdown__item.is-selected {
    background: #1f1d1d !important;
  }
  .el-select-dropdown__item {
    color: #fff !important;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: #1f1d1d !important;
    color: #fff !important;
  }
  .el-select-dropdown__item.is-hovering {
    background: #1f1d1d !important;
    color: #fff !important;
  }
  .el-popper__arrow {
    &::before {
      border: 1px solid #49659e !important;
      background: #121212 !important;
    }
  }
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
  background: none !important;
}
.el-menu--horizontal>.el-menu-item.is-active {
  border-bottom: none !important;
}
.el-menu--horizontal>.el-menu-item {
  border-bottom: none !important;
}
.el-collapse {
  --el-collapse-header-bg-color: #222222 !important;
  --el-collapse-header-text-color: #FFFFFF !important;
  --el-collapse-header-font-size: 20px !important;
  --el-collapse-header-height: 69px !important;
  --el-collapse-border-color: none !important;
  --el-collapse-content-bg-color: #222222 !important;
  border-bottom: none !important;
  border-top: none !important;
  .el-collapse-item {
    margin-bottom: 20px !important;
    .el-collapse-item__header {
      padding-left: 20px !important;
      padding-right: 12px !important;
    }
  }
  .el-collapse-item__content {
    padding: 20px !important;
    margin: 0px 20px 20px 20px !important;
    background: #333 !important;
    color: #fff !important;
  }
  
}
.el-popper.is-light {
  background: #222222;
  border: none;
  .el-menu {
    background-color: #222222;
    .el-menu-item {
      background-color: #222222;
      color: #fff;
      font-size: 0.26rem;
      margin: 0.05rem;
      padding: 20px !important;
      height: 0.5rem;
      &:hover {
        background-color: #333 !important;
        .el-anchor__list {
          background-color: #333 !important;
        }
      }
      .el-anchor__list {
        color: #fff;
        background-color: #222222;
        .el-anchor__link {
          color: #fff;
          font-size: 14px;
          font-size: 0.26rem;
        }
      }
    }
  }
  .el-menu--popup {
    padding: 10px 0px !important;
  }
}
.el-image-viewer__wrapper {
  z-index: 9999 !important;
}